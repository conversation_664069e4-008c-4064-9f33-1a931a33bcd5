server:
  port: 8397

route:
  rest: false
  sortedset: false
  path: opinion-collect

spring:
  application:
    name: freon-message-opinion-collect

antdu:
  metrics:
    clusterName: freon-message-opinion-collect

rate_limit:
  max_request_sec: 1000000

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.sortedset.OpinionCollectStorage
    classpath: com.antfact.commons.v2.model.TopicsWithOpinionPackage
    field:
      primaryKey: opinions

properties:
  storage:
    buka:
      enableAutoCommit: true
      offsetCommitFrequency: 30
      deflectLagThreshold: 500000
      producerTopic: message-opinion-collect-topic
    jobs:
      cbase_cold:
        runnable: false
        pollIsBatch: false
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.sortedset.OpinionCollectStorage
        mode: poll
        consumers:
          normal:
            threadCount: 12
            topics:
              - pubopinion_topics_tweets
              - opinion_sid_dedupped
            group: message-opinion-collect-group-cbase
            persistId: persistence-message-opinion-collect-cbase
            persistLimitSpeed: 500000
          deflect:
            threadCount: 3
            topic: message-opinion-collect-topic-deflect-cbase
            group: message-opinion-collect-group-deflect-cbase
            persistId: persistence-message-opinion-collect-cbase-deflect
            persistLimitSpeed: 500000
          failback:
            threadCount: 3
            topic: message-opinion-collect-topic-failback-cbase
            group: message-opinion-collect-group-failback-cbase
            persistId: persistence-message-opinion-collect-cbase-failback
            persistLimitSpeed: 80000

cbase:
  keyspace: yuqing_collect
  table: opinion_sorted_set_desc
  clusters:
    map:
      cold_cluster:
        enable: true
        clusterNodes: *********,**********,**********,**********,**********
        clusterName: opinion-cold-cluster
        port: 9042
        datacenter: dc-opinion
        username: cassandra
        password: cassandra
        operateTimeout: 30000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 604800

caster:
  clusterName: redis-similar-store-count-wemedia
  clusterAddress: **********:6787
  redisCodec: org.redisson.client.codec.StringCodec

persist:
  executor:
    max_thread: 20

query.pagelimit: 10000

caffeine:
  expireAfterWrite: 300
  maximumSize: 10000

collect:
  count:
    hour.limit: 420
