server:
  port: 8394

spring:
  application:
    name: recover-user-service

antdu:
  discovery:
    serverUrl:  http://metaserver.dev-antducloud.com/
  metrics:
    url:  http://metrics.dev-antducloud.com/metric/report
    clusterName: recover-user-service
    slf4jEnable: false
    jmxEnable:  false
    httpEnable: false
    slf4jTimeInterval: 60
    httpTimeInterval: 60

eureka:
  instance:
    preferIpAddress: true
  client:
    enabled: true
    serviceUrl:
      defaultZone:  http://coordinator.dev-antducloud.com/eureka/v2/
    register-with-eureka: false

rate_limit:
  service: ratelimit-grpc-server
  max_request_sec: 100000
  deviation: 0.001
  time_widows_seconds: 1
  request_timeout_seconds: 10

freon:
  entity:
    classpath: com.antfact.commons.v2.model.UserPack$User
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: userId,platform,userName,nickName,countryCode,provinceCode,cityCode,location,description,gender,createdAt,tags,vip,vipType,vipInfo,profileImageUrl,language,timeZone,blogRUL,enterprise,verified,state,university,tagsFromPlatform,ipLocation
      expandClasspath: com.antfact.commons.v2.model.UserPack$MutabilityPropertiesOnUser
      expandField: prop
      expandFixedField: followerCount,friendsCount,favouritesCount,statusesCount,likesCount

cbase:
  keyspace: graph_data
  table: blogger
  clusters:
    map:
      cold_cluster:
        enable: true
        clusterNodes: ***********,***********,***********,***********,***********
        clusterName: document-cluster-cold
        port: 9044
        datacenter: dc-opinion
        username: cassandra
        password: cassandra
        operateTimeout: 60000

max:
  request:
    time_second:  1
    capacity: 100000

log:
  print:
    interval: 10000

persistence:
  rate_limit:
    enable: true
    resourceId: recover-user-service
    qps: 200000

disruptor:
  producer:
    size: 1
  consumer:
    size: 1
  workpool:
    coreThread: 1
    threadQueueSize: 300
    keepAliveTime: 60

#source 数据源  filter 数据过滤 数据转换
generic:
  source: blogUserSource
  converter: userConvFunc

#sina, twitter, tencent, sohu
hstore:
  path: /processed/user-tidied/tencent
recover:
  begin: 20200513-00000
  end:  20200513

