package com.antdu.freon.search;

import com.antfact.commons.v2.model.UserPack;

import com.antdu.analysis.pre.AnalysisUtil;
import com.antdu.analysis.pre.AnalyzerConfig;
import com.google.common.collect.Lists;

import org.apache.http.HttpHost;
import org.apache.lucene.analysis.Analyzer;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.Node;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2022/4/24
 * 数据从索引导出，存放到本地文件
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
public class BloggerIdEsMain {
    public static void main(String... args) throws Exception {
        String host = "**********";
        int port = 9700;
        String index = "media";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
        List<String> indexes = new ArrayList<>();
        long startTime = 1747411200000L;
        while (startTime <= 1750089600000L) {
            indexes.add(index + "-" + sdf.format(new Date(startTime)));
            startTime+= 1000 * 60 * 60 * 24;
        }

        String query = "platform:wm_weixin && username:";
        String aggField = "username";
        String filePath = "/Users/<USER>/Downloads/weixin-hao1";

        BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/Downloads/weixin-hao"));
        List<String> list = new ArrayList<>();
        String str = null;
        while ((str = reader.readLine()) != null) {
            list.add(str);
        }
        System.setProperty("check.interval", "10000");
        List<HttpHost> httpHostList = new ArrayList<>();
        for (String nodeIp : host.split(",")) {
            httpHostList.add(new HttpHost(nodeIp, port));
        }
        int timeout = 60;
        log.info("query:{}", query);
        RestClientBuilder clientBuilder =
            RestClient.builder(httpHostList.toArray(new HttpHost[] {}))
                      .setFailureListener(new RestClient.FailureListener() {
                          @Override
                          public void onFailure(Node node) {
                              log.error("FAILURE !!!! FailureListener HAS WOKEN UP!!!!: "
                                            + node.toString());
                          }
                      })
                      .setRequestConfigCallback(requestConfigBuilder ->
                                                    requestConfigBuilder
                                                        .setConnectTimeout(timeout * 1000)
                                                        .setSocketTimeout(timeout * 1000)
                                                        .setConnectionRequestTimeout(timeout / 10 * 1000));
        RestHighLevelClient restHighLevelClient = new RestHighLevelClient(clientBuilder);
        BufferedWriter writer2 = new BufferedWriter(new FileWriter(filePath));
        String[] array = indexes.toArray(new String[0]);
        int count = 0;
        int acount = 0;
        List<List<String>> partition = Lists.partition(list, 100);
        for (List<String> ids : partition) {
            acount++;
            SearchRequest searchRequest = new SearchRequest(array);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            StringBuffer sb = new StringBuffer();
            sb.append("(");
            for (String id : ids) {
                if (id.isEmpty()) {
                    continue;
                }
                sb.append(id).append(" || ");
            }
            sb.delete(sb.length() - 4, sb.length());
            sb.append(")");
            System.out.println(ids.get(ids.size() - 1));
            searchSourceBuilder.query(QueryBuilders.queryStringQuery(query+sb));

            searchSourceBuilder.aggregation(AggregationBuilders.terms(aggField).field(aggField).size(100));
            searchSourceBuilder.size(1);
            searchRequest.source(searchSourceBuilder);
            System.out.println(System.currentTimeMillis());
            SearchResponse search = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            System.out.println(System.currentTimeMillis());
            if (search.getHits().getTotalHits().value != 0) {
                for (Terms.Bucket userId : ((Terms) (search.getAggregations().getAsMap().get(aggField))).getBuckets()) {
                    writer2.write(userId.getKey()+","+userId.getDocCount());
                    writer2.newLine();
                }
                writer2.flush();
                count++;
            }
        }
        writer2.flush();
        writer2.close();
        log.info("id store success,data count:{}", count);

    }
}
