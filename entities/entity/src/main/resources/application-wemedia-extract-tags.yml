server:
  port: 8395

route:
  rest: false
  path: tags

spring:
  application:
    name: freon-wemedia-extract-tags

antdu:
  metrics:
    clusterName: freon-wemedia-extract-tags

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.skinny.SkinnyRowStorage
    classpath: com.antfact.commons.v2.model.StatusPack$WeMedia
    field:
      stateType: statusType
      primaryKey: wmId
      createat: createAt

properties:
  storage:
    buka:
      producerTopic: message-wemedia-tags-topic
    jobs:
      cbase_hot:
        runnable: true
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.special.ExtractTagIgnoreTimeStampStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_event_tagged
            group: message-wemedia-tags-group-cbase-hot1
            persistId: persistence-message-wemedia-tags-cbase-hot
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-wemedia-tags-topic-deflect-cbase-hot
            group: message-wemedia-tags-group-deflect-cbase-hot
            persistId: persistence-message-wemedia-tags-cbase-hot-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-wemedia-tags-topic-failback-cbase-hot
            group: message-wemedia-tags-group-failback-cbase-hot
            persistId: persistence-message-wemedia-tags-cbase-hot-failback
            persistLimitSpeed: 30000
      cbase_cold:
        runnable: false
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.special.SkinnyRowExtractTagStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_event_tagged
            group: message-wemedia-tags-group-cbase-cold1
            persistId: persistence-message-wemedia-tags-cbase-cold
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-wemedia-tags-topic-deflect-cbase-cold
            group: message-wemedia-tags-group-deflect-cbase-cold
            persistId: persistence-message-wemedia-tags-cbase-cold-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-wemedia-tags-topic-failback-cbase-cold
            group: message-wemedia-tags-group-failback-cbase-cold
            persistId: persistence-message-wemedia-tags-cbase-cold-failback
            persistLimitSpeed: 30000
      caster:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.storage.caster.storage.special.ExtactTagsCasterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_event_tagged
            group: message-wemedia-tags-group-caster
            persistId: persistence-message-wemedia-tags-caster
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-wemedia-tags-topic-deflect-caster
            group: message-wemedia-tags-group-deflect-caster
            persistId: persistence-message-wemedia-tags-caster-deflect
            persistLimitSpeed: 30000

cbase:
  keyspace: yuqing_skinny
  table: property_tags
  clusters:
    map:
      hot_cluster:
        enable: true
        clusterNodes: 10.20.2.22,10.20.2.23
        clusterName: Hot Cluster
        port: 9042
        datacenter: cs01
        username: cassandra
        password: cassandra
        operateTimeout: 30000
        ttlMaxDays: 2
        ttlMax: 172800
        ttlMin: 3600
        sequenceNo: 0
      cold_cluster:
        enable: false
        clusterNodes: 10.20.1.108,10.20.1.117,10.20.1.120,10.20.1.123
        clusterName: property-tags-cluster-cold
        port: 9042
        datacenter: dc-opinion
        username: cassandra
        password: cassandra
        operateTimeout: 60000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 86400
        keyHasTimeStamp: true
        sequenceNo: 1

cache:
  name_pre: message.tags.

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: extract-tags-reader-ratelimiter

persist:
  executor:
    max_thread: 12