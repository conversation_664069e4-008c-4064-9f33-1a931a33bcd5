server:
  port: 8395

route:
  rest: false
  path: similar-document

spring:
  application:
    name: freon-message-similar-document

antdu:
  metrics:
    clusterName: freon-message-similar-document

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.skinny.similardoc.SimilarDocStorage
    classpath: com.antfact.commons.v2.model.StatusPack$Document
    checkPropsFieldName: props  #易变属性字段
    ignoreTimeStampSuffix: false
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: docId,contentId,title,content,trimContent,imageUrl,url,createdAt,displayTime,associateUserId,userId,userHead,userName,nickName,scope,mediaType,link,replyStatusId,replyUserId,replyUserName,retweetId,retweetUserId,retweetUserName,location,ip,ipLocation,source,originalContent,latitude,longitude,vip,vipType,siteIP,domain,channelName,keywords,imageUrls,groupId,groupName,tags,countryCode,props,originalDoc,stateType,websiteName,userDescribe,originalId,platform,tagsWithScore,important,originalSeedUrl,influence,originalLocation
    field:
      primaryKey: docId

properties:
  storage:
    buka:
      producerTopic: message-similar-document-topic
    jobs:
      caster:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.storage.caster.storage.similardoc.SimilarCasterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 3
            topics:
              - webpage_similar_tag
            group: message-similar-group-doc-caster-1
            persistId: persistence-message-similar-document-caster
            persistLimitSpeed: 50000
          deflect:
            threadCount: 3
            topic: message-similar-document-topic-deflect-caster
            group: message-similar-document-group-deflect-caster-new
            persistId: persistence-message-similar-document-caster-deflect
            persistLimitSpeed: 50000
          failback:
            threadCount: 1
            topic: message-similar-document-topic-failback-caster
            group: message-similar-document-group-failback-caster
            persistId: persistence-message-similar-document-caster-failback
            persistLimitSpeed: 30000
      cbase_hot:
        runnable: true
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.similardoc.SimilarDocStorage
        mode: poll
        consumers:
          normal:
            threadCount: 4
            topics:
              - webpage_similar_tag
            group: message-similar-document-group-cbase-hot-local
            persistId: persistence-message-similar-document-cbase-hot
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-similar-document-topic-deflect-cbase-hot
            group: message-similar-document-group-deflect-cbase-hot-local
            persistId: persistence-message-similar-document-cbase-hot-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-similar-document-topic-failback-cbase-hot
            group: message-similar-document-group-failback-cbase-hot-local
            persistId: persistence-message-similar-document-cbase-hot-failback
            persistLimitSpeed: 30000

cbase:
  keyspace: yuqing_twcs
  table: doc_view
  clusters:
    map:
      hot_cluster:
        enable: true
        clusterNodes: 10.20.2.43,10.20.2.44,10.20.2.45,10.20.2.46,10.20.2.47
        clusterName: yu-hot-cluster
        port: 9042
        datacenter: datacenter1
        username: cbase
        password: antducbaseadmin@2022
        operateTimeout: 30000
        ttlMaxDays: 14
        subTableDuration: 604800
        ttlMax: 8035200
        ttlMin: 604800
        compactType: twcs
        keyHasTimeStamp: true
        sequenceNo: 0

cache:
  name_pre: message.view

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: similar-document-reader-ratelimiter

persist:
  executor:
    max_thread: 12

readCache.enable: false
writeCache.enable: false

caster:
  enable: false
  clusterName: redis-similar-data-store-new
  clusterAddress: ***********:8000,***********:8000,***********:8000,***********:8000