/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.recover.file.format;

import com.antfact.mapreduce.google.protobuf.Descriptors;
import com.antfact.mapreduce.google.protobuf.Message;

import com.antdu.freon.recover.file.config.FileOutConfig;

import org.springframework.beans.factory.annotation.Autowired;

import java.io.Closeable;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.locks.ReentrantLock;

import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2021/8/23
 * 数据输出 抽象类
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
public abstract class AbsDataFormat implements Closeable {

    /**
     * 数据格式转换
     */
    @Autowired
    protected FileOutConfig fileOutConfig;
    /**
     * 实体字段详情 映射
     */
    @Autowired
    protected Map<String, Descriptors.FieldDescriptor> descriptorMap;
    /**
     * list
     */
    private LinkedBlockingQueue<Message> messages = new LinkedBlockingQueue<>();
    /**
     * nowCount
     */
    private final LongAdder formatCount = new LongAdder();
    /**
     * file Count
     */
    protected volatile int fileCount = 1;
    /**
     * split File Lock
     */
    private final ReentrantLock lock = new ReentrantLock();
    /**
     * current FileName
     */
    protected String currentFileName;

    /**
     * switch File
     */
    protected void switchOutPutFileStream() {
        // doNoting
    }

    private boolean preFormatData(Message data) {
        lock.lock();
        boolean flag = false;
        try {
            flag = formatData(data);
            if (flag) {
                formatCount.increment();
            }
        } finally {
            lock.unlock();
        }
        return flag;
    }

    /**
     * formatData single
     */
    protected abstract boolean formatData(Message data);

    /**
     * formatData multi
     */
    protected boolean formatDataMulti(Message data) {
        // 判断data为空的代码
        if (data == null) {
            log.warn("Message data is null, skip processing");
            return false;
        }

        messages.add(data);


        if (messages.size() > fileOutConfig.getFileBatchSize()) {
            lock.lock();
            LinkedBlockingQueue<Message> temp;
            try {
                temp = messages;
                messages = new LinkedBlockingQueue<>();
                for (Message message : temp) {
                    formatData(message);
                }
                flush();
//                messages.clear();
            } catch (IOException e) {
                log.error("flush datafile!", e);
            } finally {
                lock.unlock();
            }

            if (formatCount.sum() > fileCount * fileOutConfig.getFileSize()) {
                lock.lock();
                try {
                    switchOutPutFileStream();
                    fileCount++;
                } finally {
                    lock.unlock();
                }
            }
        }
        return true;
    }

    /**
     * FLUSH FILE
     */
    public abstract void flush() throws IOException;

    /**
     * format Data
     */
    public boolean format(Message data) {
        // 判断data为空的代码
        if (data == null) {
            log.warn("Message data is null, skip processing");
            return false;
        }

        if (fileOutConfig.isMulFile()) {
            return formatDataMulti(data);
        } else {
            return preFormatData(data);
        }
    }

    @Override
    public void close() throws IOException {
        if (messages != null && messages.size() != 0) {
            for (Message message : messages) {
                formatData(message);
            }
        }
    }
}
