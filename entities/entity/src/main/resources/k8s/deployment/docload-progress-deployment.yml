apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-docload-progress-caster
  namespace:
  labels:
    app: freon-docload-progress-caster
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-docload-progress-caster
  template:
    metadata:
      labels:
        app: freon-docload-progress-caster
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-docload-progress-caster
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=docload-progressV2.yml,application-commons.yml"
            - "-Dapp_name=freon-docload-progress-caster"
            - "-Dproperties.storage.jobs.caster.runnable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: caster-config
              mountPath: /home/<USER>
      volumes:
        - name: caster-config
          configMap:
            name: freon-message-entities-cm

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-docload-progress-cbase-hot
  namespace:
  labels:
    app: freon-docload-progress-cbase-hot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-docload-progress-cbase-hot
  template:
    metadata:
      labels:
        app: freon-docload-progress-cbase-hot
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-docload-progress-cbase-hot
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=docload-progressV2.yml,application-commons.yml"
            - "-Dapp_name=freon-docload-progress-cbase-hot"
            - "-Dproperties.storage.jobs.cbase_hot.runnable=true"
            - "-Dcbase.clusters.map.hot_cluster.enable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: cbase-config
              mountPath: /home/<USER>
      volumes:
        - name: cbase-config
          configMap:
            name: freon-message-entities-cm
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-docload-progress-caster-test
  namespace:
  labels:
    app: freon-docload-progress-caster-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-docload-progress-caster-test
  template:
    metadata:
      labels:
        app: freon-docload-progress-caster-test
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-docload-progress-caster
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=docload-progress-test.yml,application-commons.yml"
            - "-Dapp_name=freon-docload-progress-caster"
            - "-Dproperties.storage.jobs.caster.runnable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: caster-config
              mountPath: /home/<USER>
      volumes:
        - name: caster-config
          configMap:
            name: freon-message-entities-cm

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-docload-progress-cbase-hot-test
  namespace:
  labels:
    app: freon-docload-progress-cbase-hot-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-docload-progress-cbase-hot-test
  template:
    metadata:
      labels:
        app: freon-docload-progress-cbase-hot-test
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-docload-progress-cbase-hot
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=docload-progress-test.yml,application-commons.yml"
            - "-Dapp_name=freon-docload-progress-cbase-hot"
            - "-Dproperties.storage.jobs.cbase_hot.runnable=true"
            - "-Dcbase.clusters.map.hot_cluster.enable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: cbase-config
              mountPath: /home/<USER>
      volumes:
        - name: cbase-config
          configMap:
            name: freon-message-entities-cm