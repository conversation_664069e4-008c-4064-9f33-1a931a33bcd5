server:
  port: 8864

route:
  rest: true
  path: document

spring:
  application:
    name: freon-message-document

antdu:
  metrics:
    clusterName: freon-message-document

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.skinny.SkinnyRowStorage
    classpath: com.antfact.commons.v2.model.StatusPack$Document
    isSendStreamCheck: true #是否发送streamcheck
    checkPropsFieldName: props  #易变属性字段
    ignoreTimeStampSuffix: true #是否忽略时间戳后缀
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: docId,contentId,title,content,trimContent,imageUrl,url,createdAt,displayTime,associateUserId,userId,userHead,userName,nickName,scope,mediaType,link,replyStatusId,replyUserId,replyUserName,retweetId,retweetUserId,retweetUserName,location,ip,ipLocation,source,originalContent,latitude,longitude,vip,vipType,siteIP,domain,channelName,keywords,imageUrls,groupId,groupName,countryCode,stateType,websiteName,userDescribe,originalId,platform,tagsWithScore,important,originalSeedUrl,influence,originalLocation,videoUrl,videoSnapshotUrl
    field:
      primaryKey: docId
      createat: createdAt
      source: source
      delCache: important

properties:
  storage:
    buka:
      offsetCommitInterval: 5
      producerTopic: message-document-topic
    jobs:
      cbase_hot:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.SkinnyRowStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - webpage_id_dedupped
            group: message-document-group-cbase-hot
            persistId: persistence-message-document-cbase-hot
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-document-topic-deflect-cbase-hot
            group: message-document-group-deflect-cbase-hot
            persistId: persistence-message-document-cbase-hot-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-document-topic-failback-cbase-hot
            group: message-document-group-failback-cbase-hot
            persistId: persistence-message-document-cbase-hot-failback
            persistLimitSpeed: 30000
      cbase_cold:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.SkinnyRowStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - webpage_id_dedupped
            group: message-document-group-cbase-cold
            persistId: persistence-message-document-cbase-cold
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-document-topic-deflect-cbase-cold
            group: message-document-group-deflect-cbase-cold
            persistId: persistence-message-document-cbase-cold-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-document-topic-failback-cbase-cold
            group: message-document-group-failback-cbase-cold
            persistId: persistence-message-document-cbase-cold-failback
            persistLimitSpeed: 30000
      caster:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.storage.caster.storage.GeneralCasterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - webpage_id_dedupped
            group: message-document-group-caster
            persistId: persistence-message-document-caster
            persistLimitSpeed: 30000
          deflect:
            threadCount: 6
            topic: message-document-topic-deflect-caster
            group: message-document-group-deflect-caster
            persistId: persistence-message-document-caster-deflect
            persistLimitSpeed: 30000

cbase:
  keyspace: yuqing_skinny
  table: document
  clusters:
    map:
      hot_cluster:
        enable: false
        clusterNodes: 10.20.2.22,10.20.2.23
        clusterName: Hot Cluster
        port: 9042
        datacenter: cs01
        username: cassandra
        password: cassandra
        operateTimeout: 30000
        ttlMaxDays: 2
        ttlMax: 172800
        ttlMin: 3600
        sequenceNo: 0
      original_cluster:
        enable: false
        clusterNodes: 10.20.2.22,10.20.2.23
        clusterName: Hot Cluster
        port: 9042
        datacenter: cs01
        username: cassandra
        password: cassandra
        operateTimeout: 30000
        ttlMaxDays: 7
        ttlMax: 604800
        ttlMin: 86400
        sequenceNo: 1
      cold_cluster:
        enable: true
        clusterNodes: **********,***********,***********
        clusterName: document-cluster-cold
        port: 9044
        datacenter: dc-opinion
        username: cassandra
        password: cassandra
        operateTimeout: 20
        connectionMaxRequests: 10000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 86400
        sequenceNo: 2

cache:
  name_pre: message.document.

caster-clusters:
  map:
    readCaster:
      enable: true
      clusterName: redis-yuqing-reader-web
      clusterAddress: **********:6700,**********:7702,**********:7700,**********:6701,**********:7701,**********:6702
      redisCodec: org.redisson.client.codec.ByteArrayCodec
      redisThreads: 16
      redisNettyThreads: 32
      nodeConnectionPoolSize: 64
      nodeConnectionMinSize: 5
      decodeInexecutor: true
      transportModeEPOLL: false
      timeout: 10000

readCache:
  readStringCache: true
  casterName: readCaster #是caster-clusters中配置的名字
  maxSize: 400000

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: document-reader-ratelimiter

persist:
  executor:
    max_thread: 18