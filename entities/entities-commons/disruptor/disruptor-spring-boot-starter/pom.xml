<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
  ~ 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>disruptor</artifactId>
        <groupId>com.antdu.freon.entities.commons</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>Antdu.com :: Freon Entities Commons Disruptor Spring Boot Starter</name>
    <artifactId>disruptor-spring-boot-starter</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.antdu.freon.entities.commons</groupId>
            <artifactId>disruptor-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


</project>
