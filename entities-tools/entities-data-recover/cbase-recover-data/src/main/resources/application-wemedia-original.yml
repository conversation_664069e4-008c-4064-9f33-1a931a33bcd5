server:
  port: 8563

spring:
  application:
    name: recover-wemedia-original-service

antdu:
  discovery:
    serverUrl: http://metaserver.dev-antducloud.com/
  metrics:
    url: http://metrics-prom.dev-antducloud.com/metric/report
    clusterName: recover-wemedia-original-service
    slf4jEnable: false
    jmxEnable: false
    httpEnable: true
    slf4jTimeInterval: 60
    httpTimeInterval: 60

eureka:
  instance:
    preferIpAddress: true
  client:
    enabled: true
    serviceUrl:
      defaultZone: http://coordinator.dev-antducloud.com/eureka/v2/
    register-with-eureka: false

rate_limit:
  service: ratelimit-grpc-server
  max_request_sec: 100000
  deviation: 0.01
  time_widows_seconds: 1
  request_timeout_seconds: 10

freon:
  entity:
    classpath: com.antfact.commons.v2.model.StatusPack$WeMedia
    messageFields: #固定字段 / 扩展字段，逗号分格
      fixed: wmId,createdAt
    field:
      primaryKey: wmId
      createat: createdAt
    filter:
      field: wmType
      value: ORIGINAL

buka:
  bootstrapServers: yu-buka1:9096,yu-buka2:9096,yu-buka3:9096,yu-buka4:9096,yu-buka5:9096,yu-buka6:9096
  producerTopic: recorder-wemedia-original-data

cbase:
  keyspace: yuqing_skinny
  table: wemedia_original_createat
  clusters:
    map:
      hot_cluster:
        enable: true
        clusterNodes: **********,**********
        clusterName: Hot Cluster
        port: 9042
        datacenter: cs01
        username: cbase
        password: antducbaseadmin@2022
        operateTimeout: 20000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 86400
        sequenceNo: 0

max:
  request:
    time_second: 1
    capacity: 100000
log:
  print:
    interval: 60000

persistence:
  rate_limit:
    enable: true
    resourceId: recover-wemedia-original-service
    qps: 100000

recover:
  begin: '20230531-11'
  end: '20230529'

hstore:
  path: /processed/doc-tidied/wemedia  # 规整之后的目录

disruptor:
  producer:
    size: 2
  consumer:
    size: 2
  workpool:
    coreThread: 4
    threadQueueSize: 300
    keepAliveTime: 60

#source 数据源  filter 数据过滤 数据转换
generic:
  source: wemediaOrcSource
  converter: timeSuffixConv # 时间戳转换
  filter: protoV9FieldFilter
