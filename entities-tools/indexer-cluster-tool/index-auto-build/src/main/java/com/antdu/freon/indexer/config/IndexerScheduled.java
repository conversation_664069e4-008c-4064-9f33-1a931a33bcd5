package com.antdu.freon.indexer.config;


import org.apache.http.HttpHost;
import org.elasticsearch.action.admin.indices.alias.Alias;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.invoke.LambdaConversionException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 9/27/22
 * 自动创建索引
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Component
@Data
@Slf4j
public class IndexerScheduled {

    /**
     * 线程池
     */
    @SuppressWarnings("checkstyle:MagicNumber")
    private static ThreadPoolExecutor executor =
        new ThreadPoolExecutor(4, 6, 30, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10));
    /**
     * mapping
     */
    @Resource(name = "clusterMapping")
    private ClusterMapping mapping;

    /**
     * 循环调度
     */
    @SuppressWarnings("checkstyle:MagicNumber")
    @Scheduled(cron = "${indexer.scheduled:0 */1 * * * ?}")
    public void scheduledOpr() throws ExecutionException, InterruptedException {
        Map<String, Future<Boolean>> tasks = new HashMap<>();
        mapping.getClusterMapping().values().forEach(
            v -> {
                try {
                    tasks.put(v.getClusterName(), executor.submit(new CreatedAndDeleteIndexer(v)));
                } catch (Exception e) {
                    log.error("{} : execute task", v.getClusterName(), e);
                }
            });
        Set<Map.Entry<String, Future<Boolean>>> entries = tasks.entrySet();
        int step = 0;
        do {
            Iterator<Map.Entry<String, Future<Boolean>>> iterator = entries.iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Future<Boolean>> next = iterator.next();
                if (next.getValue().isDone()) {
                    log.warn("{} task done reslut:{}", next.getKey(), next.getValue().get());
                    iterator.remove();
                } else {
                    log.warn("{} task not done", next.getKey());
                }
            }
            Thread.sleep(30000); // 休息30秒
        } while (step++ < 15 && entries.iterator().hasNext());

    }

    /**
     * init call
     * @throws ExecutionException ExecutionException
     * @throws InterruptedException InterruptedException
     */
    @PostConstruct
    public void init() throws ExecutionException, InterruptedException {
        scheduledOpr();
    }
    /**
     * 创建 删除索引 任务
     */
    static class CreatedAndDeleteIndexer implements Callable<Boolean> {
        /**
         * 小时 毫秒
         */
        public static final long HOUR_MS = 1000 * 60 * 60;
        /**
         * 集群 配置
         */
        private final ClusterNodeConfig config;

        /**
         * created Thread
         */
        public CreatedAndDeleteIndexer(ClusterNodeConfig config) {
            this.config = config;
        }

        @Override
        public Boolean call() throws IOException, ParseException {
            RestHighLevelClient restHighLevelClient = newRestClient(config.getHost(), config.getPort());
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(config.getDataFormat());
            Set<String> set = new HashSet<>();
            long now = System.currentTimeMillis();
            long end = now + (config.getCreatedDay() * HOUR_MS * 24);
            long interval = config.getIntervalTime() * HOUR_MS;
            Set<String> deleteIndex = new HashSet<>();
            // config.getIndexName() + "-" + simpleDateFormat.format(now - config.getTtl());
            for (long i = now - config.getTtl(); i <= end; i += interval) {
                set.add(config.getIndexName() + "-" + simpleDateFormat.format(i));
            }
            String[] indices = null;
            try {
                indices =
                    restHighLevelClient.indices()
                                       .get(new GetIndexRequest(config.getIndexName()), RequestOptions.DEFAULT)
                                       .getIndices();
            } catch (Exception e) {
                log.error("indices get error", e);
            }
            if (indices != null && indices.length != 0) {
                Set<String> load = Arrays.stream(indices).collect(Collectors.toSet());
                load.forEach(set::remove);

                // build delete Index
                Date lateDate = simpleDateFormat.parse(simpleDateFormat.format(now - config.getTtl()));
                for (String indexName : load) {
                    String[] split = indexName.split("-");
                    String date = split[split.length - 1];
                    Date time = simpleDateFormat.parse(date);
                    if (lateDate.compareTo(time) > 0) {
                        deleteIndex.add(indexName);
                    }
                }
                log.info("remove indexes:{}", deleteIndex);
            }
            log.info("created indexer:{}", set);
            for (String index : set) {
                boolean exists =
                    restHighLevelClient.indices().exists(new GetIndexRequest(index), RequestOptions.DEFAULT);
                if (exists) {
                    log.warn("{} indexer is exists!", index);
                } else {
                    log.info("index : {}, create index.", index);
                    CreateIndexRequest createIndexRequest = new CreateIndexRequest(index);
                    createIndexRequest.alias(new Alias(config.getIndexName()));
                    try {
                        restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
                    } catch (Exception e) {
                        log.error("create index error! :{}", index);
                    }
                }
            }

            for (String index : deleteIndex) {
                try {
                    AcknowledgedResponse delete =
                        restHighLevelClient.indices()
                                           .delete(new DeleteIndexRequest(index), RequestOptions.DEFAULT);
                } catch (Exception e) {
                    log.error("del index error! :{}", index);
                }
            }


            return Boolean.TRUE;
        }
    }

    /**
     * 创建 client 连接
     *
     * @param host es host
     * @param port port
     * @return RestHighLevelClient
     */
    public static RestHighLevelClient newRestClient(String host, int port) {
        RestClientBuilder builder = RestClient.builder(Arrays.stream(host.split(","))
                                                             .map(a -> new HttpHost(a, port))
                                                             .toArray(HttpHost[]::new))
                                              .setRequestConfigCallback(requestConfigBuilder -> {
                                                  requestConfigBuilder.setConnectTimeout(5000);
                                                  requestConfigBuilder.setSocketTimeout(60000);
                                                  requestConfigBuilder.setConnectionRequestTimeout(1000);
                                                  return requestConfigBuilder;
                                              });
        return new RestHighLevelClient(builder);
    }
}
