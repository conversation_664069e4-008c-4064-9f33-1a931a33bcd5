/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.recover.cassandra.recover;

import com.antfact.mapreduce.google.protobuf.GeneratedMessageV3;

import com.google.common.base.Strings;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * <p>自媒体类型过滤持久器 </p>
 * <p>
 * 创建日期： 2021/7/1
 *
 * @param <T> 存储实体泛型
 * @param <B> 存储实体builder泛型
 * <AUTHOR> tiantian(<EMAIL>)
 */
@Component
@Profile({"shortvideo"})
public class WemediaTypeFilterRecover<T, B extends GeneratedMessageV3.Builder<B>>
    extends SkinnyRowRecover<T, B> {

    /**
     * 过滤自媒体类型，例如：SHORT_VIDEO 短视频
     * 空 不过滤
     * SHORT_VIDEO 只存储指定类型
     * -SHORT_VIDEO 不存储指定类型
     */
    @Value("${freon.entity.filter.mediaType:}")
    protected String mediaTypeFilter;
    /**
     * 媒体类型字段
     */
    @Value("${freon.entity.field.mediaType:mediaType}")
    protected String mediaType;


    @Override
    public void persist(T t) {
        //过滤条件
        if (dataFilter(t)) {
            super.persist(t);
        }
    }

    /**
     * 自媒体媒体类型过滤
     *
     * @return true:正常存储 false:数据筛除
     */
    protected boolean dataFilter(T m) {
        if (Strings.isNullOrEmpty(mediaTypeFilter)) {
            return true;
        }
        //对象媒体类型
        String curType = getFieldValue(m, mediaType).toString();

        //过滤类型不以"-"开头，只存储指定类型
        if (!mediaTypeFilter.startsWith("-")) {
            return mediaTypeFilter.equals(curType);
        }
        //过滤类型以"-"开头，不存储指定类型
        String actualMediaType = mediaTypeFilter.substring(1).trim();
        return !actualMediaType.equals(curType);
    }
}
