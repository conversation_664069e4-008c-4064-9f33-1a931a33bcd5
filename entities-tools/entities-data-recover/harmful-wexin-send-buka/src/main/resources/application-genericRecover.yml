server:
  port: 8394

spring:
  application:
    name: recover-wemedia-user-service

antdu:
  discovery:
    serverUrl:  http://metaserver.dev-antducloud.com/
  metrics:
    url:  http://metrics.dev-antducloud.com/metric/report
    clusterName: recover-wemedia-user-service
    slf4jEnable: false
    jmxEnable:  false
    httpEnable: false
    slf4jTimeInterval: 60
    httpTimeInterval: 60

eureka:
  instance:
    preferIpAddress: true
  client:
    enabled: true
    serviceUrl:
      defaultZone:  http://coordinator.dev-antducloud.com/eureka/v2/
    register-with-eureka: false

rate_limit:
  service: ratelimit-grpc-server
  max_request_sec: 100000
  deviation: 0.001
  time_widows_seconds: 1
  request_timeout_seconds: 10

freon:
  entity:
    classpath: com.antfact.commons.v2.model.StatusPack$Status
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: userId,platform,userName,nickName,countryCode,provinceCode,cityCode,location,description,gender,createdAt,tags,vip,vipType,vipInfo,profileImageUrl,language,timeZone,blogRUL,enterprise,verified,state,university,tagsFromPlatform,ipLocation
#      expandClasspath: com.antfact.commons.v2.model.UserPack$MutabilityPropertiesOnUser
#      expandField: prop
#      expandFixedField: followerCount,friendsCount,favouritesCount,statusesCount,likesCount
      primaryId: statusId

cbase:
  keyspace: graph_data
  table: wemedia_user
  clusters:
    map:
      cold_cluster:
        enable: true
        clusterNodes: ***********,***********,***********,***********,***********
        clusterName: document-cluster-cold
        port: 9044
        datacenter: dc-opinion
        username: cassandra
        password: cassandra
        operateTimeout: 60000

hadoop.home.admin: hdfs
max:
  request:
    time_second:  1
    capacity: 100000

log:
  print:
    interval: 60000

persistence:
  rate_limit:
    enable: true
    resourceId: recover-wemedia-user-service
    qps: 200000

hstore:
  path: /Users/<USER>/Downloads/
recover:
  begin: sina-part-r-00000
  end:  sina-part-r-00000
  file.isLocal: true

generic:
  source: documentV8Source
#  converter: userActinfConvFunc
#  filter: userActinfFilter

#  filter: weMediaUserNickNameFilter

disruptor:
  producer:
    size: 4
  consumer:
    size: 4
  workpool:
    coreThread: 4
    threadQueueSize: 300
    keepAliveTime: 60
file:
  buka:
    topic: tweet_id_dedupped_1
    host: buka01:9096,buka02:9096,buka03:9096
  csv:
    field: userId
  store:
    mulFile: false
    formatType: bukaFormat
    outputPath: "/Users/<USER>/"
    fileName: 'data'