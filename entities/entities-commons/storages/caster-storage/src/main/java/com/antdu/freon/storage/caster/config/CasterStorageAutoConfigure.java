/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.storage.caster.config;

import com.antdu.freon.cache.CacheBuilder;
import com.antdu.freon.cache.ReaderCache;
import com.antdu.freon.cache.WriterCache;
import com.antdu.freon.cache.caster.CasterConfig;
import com.antdu.freon.cache.exception.UnknonwCacheException;
import com.antdu.freon.cache.spi.CacheProvider;
import com.antdu.freon.commons.utils.ProtobufMessageUtils;
import com.antdu.freon.commons.utils.RestUtils;
import com.antdu.freon.storage.caster.codec.ProbufCodec;
import com.antdu.freon.storage.caster.service.CasterPersistenceService;
import com.antdu.freon.storage.caster.service.CasterStreamCheck;
import com.antdu.freon.storage.caster.storage.GeneralCasterStorage;
import com.antdu.freon.storage.caster.storage.OriginalFieldCasterStorage;
import com.antdu.freon.storage.caster.storage.SpecifiedFieldCasterStorage;
import com.antdu.freon.storage.caster.storage.TopicHashCasterStorage;
import com.antdu.freon.storage.caster.storage.opinion.HarmfulOpinionCollectCounterStorage;
import com.antdu.freon.storage.caster.storage.opinion.OpinionCollectCounterStorage;
import com.antdu.freon.storage.caster.storage.opinion.OpinionCollectStorage;
import com.antdu.freon.storage.caster.storage.similardoc.SimilarCasterStorage;
import com.antdu.freon.storage.caster.storage.similardoc.UpdateSimilarCasterStorage;
import com.antdu.freon.storage.caster.storage.special.ClearCacheStorage;
import com.antdu.freon.storage.caster.storage.special.CrawlTimeCasterStorage;
import com.antdu.freon.storage.caster.storage.special.ExtactTagsCasterStorage;
import com.antdu.freon.storage.caster.storage.special.MutapropCasterStorage;
import com.antdu.freon.storage.caster.storage.special.SimilarCountCasterStorage;
import com.antdu.freon.utils.ConstantUtils;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;

import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;
import scala.Function2;

/**
 * <p>带有读写缓存的storage通用配置类</p>
 * <p/>
 * 创建日期 2019/5/7
 *
 * @param <T> 读写缓存存储value类型
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "caster.enable", havingValue = "true", matchIfMissing = true)
//@ConditionalOnExpression("#{environment['properties.storage.jobs.caster.runnable']!=null}")
@Import({CasterPersistenceService.class, GeneralCasterStorage.class, CasterStreamCheck.class,
    CasterStorageJobConfiguration.class, MutapropCasterStorage.class, TopicHashCasterStorage.class,
    SimilarCountCasterStorage.class, ClearCacheStorage.class,
    SimilarCasterStorage.class, UpdateSimilarCasterStorage.class,
    ExtactTagsCasterStorage.class,
    HarmfulOpinionCollectCounterStorage.class, OpinionCollectCounterStorage.class, OpinionCollectStorage.class,
    SpecifiedFieldCasterStorage.class, OriginalFieldCasterStorage.class, CrawlTimeCasterStorage.class})
public class CasterStorageAutoConfigure<T> {

    /**
     * 缓存名前缀
     */
    @Value("${cache.name_pre:}")
    protected String cacheNamePre;

    /**
     * 写缓存过期时间
     */
    @Value("${writeCache.ttl.seconds:1800}")
    protected int writeCacheTtlSeconds;
    /**
     * 写缓存是否开启
     */
    @Value("${writeCache.enable:true}")
    protected boolean writeCacheEnable;
    /**
     * 读缓存是否开启
     */
    @Value("${readCache.enable:true}")
    protected boolean readCacheEnable;

    /**
     * 读缓存过期时间 默认不过期
     */
    @Value("${readCache.ttl.seconds:0}")
    protected int readerCacheTtlSeconds;

    /**
     * 读缓存大小
     */
    @Value("${readCache.maxSize:100000}")
    protected int readCacheMaxSize;

    /**
     * protobuf实体路径名 如:com.antfact.commons.v2.model.StatusPack$Status
     */
    @Value("${freon.entity.classpath}")
    protected String protoClassName;

    /**
     * 实体主键名
     */
    @Value("${freon.entity.field.primaryKey}")
    private String primaryKey;

    /**
     * message 字段映射bean
     */
    @Autowired
    private Map<String, Descriptors.FieldDescriptor> descriptorMap;
    /**
     * cache 提供
     */
    @Autowired
    private CacheProvider cacheProvider;

    /**
     * 读缓存 lru还是string类型
     */
    @Value("${readCache.readStringCache:false}")
    private boolean readStringCache;

    /**
     * 读缓存 使用的caster名字
     */
    @Value("${readCache.casterName:}")
    private String readCasterName;

    /**
     * 读缓存 名称是否有类型后缀
     */
    @Value("${readCache.nameHasTypeSuffix:true}")
    private boolean nameHasTypeSuffix;

    /**
     * 设置 key 函数
     */
    private Function2<T, String, T> keyBuilder = (msg, key) ->
        (T) ((Message) msg).toBuilder().setField(descriptorMap.get(primaryKey), key).build();
    /**
     * key 去时间戳 函数
     */
    private Function<String, String> keyDelTimestampFunction = k -> RestUtils.docidDelTimeStamp(k);

    /**
     * 构建写缓存
     *
     * @return
     */
    @Bean
    protected WriterCache<String, T> buildWriterCache() {
        if (Strings.isBlank(cacheNamePre) || !writeCacheEnable) {
            log.info("cache.name_pre config is null or write cache is not enable, no writerCache will be created.");
            return null;
        }
        String writeCacheName = getWriteCacheName(cacheNamePre);
        Properties properties = new Properties();
        properties.put(writeCacheName + CasterConfig.CASTER_VALUE_CODEC, ProbufCodec.INSTANCE);
        properties.put(writeCacheName + CasterConfig.CASTER_VALUE_CLASS,
                       ProtobufMessageUtils.getMessage(protoClassName).getClass());

        try {
            return CacheBuilder.<String, T>create(properties)
                .name(writeCacheName).cacheProvider(cacheProvider)
                .handleFunction(keyDelTimestampFunction, keyBuilder)
                .writer(writeCacheTtlSeconds,
                        TimeUnit.SECONDS, msg -> keyDelTimestampFunction.apply(
                        (String) ((Message) msg).getField(descriptorMap.get(primaryKey))))
                .build();
        } catch (UnknonwCacheException e) {
            log.error("WriterCache build error", e);
            return null;
        }
    }

    /**
     * 构建读缓存
     *
     * @return
     */
    @Bean
    protected ReaderCache<String, T> buildReaderCache() {
        if (Strings.isBlank(cacheNamePre) || !readCacheEnable) {
            log.info("cache.name_pre config is null or read cache is not enable, no readerCache will be created.");
            return null;
        }
        String readerCacheName = nameHasTypeSuffix ? getReaderCacheName(cacheNamePre) : cacheNamePre;
        Properties properties = new Properties();
        properties.put(readerCacheName + CasterConfig.CASTER_MAP_SLICE, true);
        properties.put(readerCacheName + CasterConfig.CASTER_KEY_CODEC, ProbufCodec.INSTANCE);
        properties.put(readerCacheName + CasterConfig.CASTER_VALUE_CODEC, ProbufCodec.INSTANCE);
        properties.put(readerCacheName + CasterConfig.CASTER_VALUE_CLASS,
                       ProtobufMessageUtils.getMessage(protoClassName).getClass());
        properties.put(readerCacheName + CasterConfig.CASTER_CHOOSE_NAME, readCasterName);
        if (readStringCache) {
            properties.put(ConstantUtils.READ_STRING_CACHE, ConstantUtils.READ_STRING_CACHE);
        }

        try {
            CacheBuilder<String, T> cacheBuilder = CacheBuilder.<String, T>create(properties)
                .name(readerCacheName).cacheProvider(cacheProvider)
                .handleFunction(keyDelTimestampFunction, keyBuilder);
            if (readerCacheTtlSeconds > 0) {
                cacheBuilder.reader(readerCacheTtlSeconds, TimeUnit.SECONDS, readCacheMaxSize);
            } else {
                cacheBuilder.reader(readCacheMaxSize);
            }
            return cacheBuilder.build();
        } catch (UnknonwCacheException e) {
            log.error("readerCache build error", e);
            return null;
        }
    }

    /**
     * 获取写缓存名称
     *
     * @param cacheNamePre
     * @return
     */
    private String getWriteCacheName(String cacheNamePre) {
        return cacheNamePre + "Writer";
    }

    /**
     * 获取读缓存名称
     *
     * @param cacheNamePre
     * @return
     */
    private String getReaderCacheName(String cacheNamePre) {
        return cacheNamePre + "Reader";
    }
}
