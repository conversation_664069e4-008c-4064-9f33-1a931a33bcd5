/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.recover.core.runable.document;


import com.antfact.commons.v2.model.StatusPack;
import com.antfact.mapreduce.google.protobuf.Message;

import com.antdu.freon.commons.utils.RestUtils;
import com.antdu.freon.persistence.exception.PersistException;

import java.io.IOException;
import java.util.function.Function;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>recover similar-document data runner</p>
 * <p/>
 * 创建日期 2020/12/21
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Slf4j
//@Component
//@Profile({"similar-document"})
public class RecoverSimilarDocumentDataRunner extends AbsRecoverDocumentDataRunner<StatusPack.Document> {

    @Override
    protected Function<Message, StatusPack.Document> getConverterData() {
        return document -> {
            try {
                //document docid需要去掉时间戳后缀存储
                StatusPack.Document converterDocument =  StatusPack.Document.parseFrom(document.toByteArray());
                return converterDocument.toBuilder()
                                        .setDocId(RestUtils.docidDelTimeStamp(converterDocument.getDocId()))
                                        .build();
            } catch (IOException e) {
                log.error("data converter fail!", e);
                throw new PersistException(e.getMessage());
            }
        };
    }
}
