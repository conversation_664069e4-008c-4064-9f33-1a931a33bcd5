/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.recover.cassandra.utils;

import com.antfact.mapreduce.google.protobuf.Descriptors;

import com.antdu.freon.commons.commons.Commons;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>cassandra cql构造器</p>
 * <p/>
 * 创建日期 2020/4/30
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Component
public class CqlMRBuilder {

    /**
     * 实体字段详情 映射
     */
    @Autowired
    protected Map<String, Descriptors.FieldDescriptor> descriptorMap;
    /**
     * 实体字段详情 映射
     */
    @Autowired
    protected Map<String, Descriptors.FieldDescriptor> expandDescriptorMap;

    /**
     * 扩展字段set集合
     */
    @Autowired
    @Qualifier(value = "extensionSet")
    protected Set<String> extensionSet;

    /**
     * 固定字段set集合
     */
    @Autowired
    @Qualifier(value = "fixedFieldSet")
    protected Set<String> fixedFieldSet;

    /**
     * 固定字段set集合
     */
    @Autowired
    @Qualifier(value = "expandFixedFieldSet")
    protected Set<String> expandFixedFieldSet;

    /**
     * protobuf 主键字段
     */
    @Value("${freon.entity.field.primaryKey:}")
    protected String primaryKeyName;

    /**
     * 需要修改的字段
     */
    @Value("${freon.entity.updateFields:}")
    protected String updateFields;

    /**
     * skinnyrow 构建查询cql
     *
     * @return
     */
    public String skinnyRowSaveCql() {
        return extensionSet.isEmpty() ? skinnySaveCql() : skinnySaveCqlWithExtension();
    }


    /**
     * widerow 写入cql
     */
    public static final String WIDE_ROW_SAVE_CQL =
        "INSERT INTO %s.%s (key, column1, value) VALUES (?,?,?) USING TTL ? ;";
    /**
     * widerow 写入cql
     */
    public static final String WIDE_ROW_SAVE_CQL_TWO =
        "INSERT INTO %s.%s (key, column1, column2, value) VALUES (?,?,?,?);";
    /**
     * widerow 写入cql, 有ttl
     */
    public static final String WIDE_ROW_SAVE_CQL_TWO_TTL =
        "INSERT INTO %s.%s (key, column1, column2, value) VALUES (?,?,?,?) USING TTL ? ;";


    /**
     * skinnyrow 构建写入cql 不处理extension 扩展字段
     *
     * @return
     */
    private String skinnySaveCql() {
        StringBuilder builder = new StringBuilder();
        builder.append("INSERT INTO %s.%s (");

        builderCqlField(builder);

        builder.deleteCharAt(builder.length() - 1).append(") VALUES (");  //extension扩展字段单独处理

        for (int i = fixedFieldSet.size(); i > 0; i--) {
            builder.append("?,");
        }
        builder.deleteCharAt(builder.length() - 1).append(") USING TTL ? ;");
        return String.valueOf(builder);
    }

    /**
     * skinnyrow 构建写入cql 不处理extension 扩展字段
     *
     * @return
     */
    public String skinnySaveCqlWithoutTtl() {
        StringBuilder builder = new StringBuilder();
        builder.append("INSERT INTO %s.%s (");

        builderCqlField(builder);

        appendCqlField(builder, expandDescriptorMap, expandFixedFieldSet);

        builder.deleteCharAt(builder.length() - 1).append(") VALUES (");  //extension扩展字段单独处理

        for (int i = fixedFieldSet.size() + expandFixedFieldSet.size(); i > 0; i--) {
            builder.append("?,");
        }
        builder.deleteCharAt(builder.length() - 1).append(");");
        return String.valueOf(builder);
    }

    /**
     * skinnyrow 构建写入cql 含有extension 扩展字段
     *
     * @return
     */
    private String skinnySaveCqlWithExtension() {
        StringBuilder builder = new StringBuilder();
        builder.append("INSERT INTO %s.%s (");

        builderCqlField(builder);

        builder.append(Commons.EXTENSION).append(") VALUES (");  //extension扩展字段单独处理

        for (int i = fixedFieldSet.size(); i > 0; i--) {
            builder.append("?,");
        }
        builder.append("?) USING TTL ? ;");
        return String.valueOf(builder);
    }

    public void builderCqlField(StringBuilder builder) {
        appendCqlField(builder, descriptorMap, fixedFieldSet);
    }

    /**
     * 根据字段映射和固定字段set构建cql 字段
     *
     * @param builder
     * @param descriptorMap
     * @param fixedFieldSet
     */
    public void appendCqlField(StringBuilder builder,
                               Map<String, Descriptors.FieldDescriptor> descriptorMap, Set<String> fixedFieldSet) {
        descriptorMap.entrySet().stream()
                     .filter(entry -> fixedFieldSet.contains(entry.getKey())
                         || fixedFieldSet.contains(entry.getValue().getName()))
                     .forEach(entry -> builder.append(entry.getKey().toLowerCase()).append(","));
    }
    /**
     * skinnyrow 构建更新cql方法,更新某些字段的值
     *
     * @return
     */
    public String skinnyUpdateCql() {
        return StringUtils.isBlank(updateFields) ? StringUtils.EMPTY : skinnyUpdateCql(updateFields, primaryKeyName);
    }

    /**
     * skinnyrow 构建更新cql方法,更新某些字段的值
     *
     * @param updateFields   更新字段，逗号分隔
     * @param primaryKeyName 主键名
     * @return
     */
    public static String skinnyUpdateCql(String updateFields, String primaryKeyName) {
        Set<String> updateFieldSet = Arrays.stream(updateFields.split(","))
                                           .collect(Collectors.toCollection(LinkedHashSet::new));
        StringBuilder builder = new StringBuilder();
        builder.append("UPDATE %s.%s SET ");

        updateFieldSet.forEach(s -> builder.append(s.toLowerCase()).append(" = ? AND "));
        builder.delete(builder.lastIndexOf("AND"), builder.length());
        builder.append("WHERE ").append(primaryKeyName.toLowerCase()).append(" = ? USING TTL ? ;");
        return builder.toString();
    }


}
