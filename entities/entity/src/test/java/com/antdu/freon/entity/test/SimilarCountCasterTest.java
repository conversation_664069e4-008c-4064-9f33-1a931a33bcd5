/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.entity.test;

import com.antfact.caster.api.Caster;
import com.antfact.caster.api.CasterBuilder;
import com.antfact.caster.api.CasterProperties;
import com.antfact.caster.model.CasterSet;

import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.redisson.client.codec.StringCodec;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.TimeZone;

/**
 * <p>freon caster 删除 </p>
 * <p>
 * 创建日期： 2021/2/19
 *
 * <AUTHOR> tiant<PERSON>(huang<PERSON><EMAIL>)
 */
public class SimilarCountCasterTest {
    private static final String CASTER_CLUSTER_ADDRESS = "**********:6787";
    private static Caster caster = null;

    /**
     * murmur hash
     */
    @SuppressWarnings("UnstableApiUsage")
    public static final HashFunction MURMUR = Hashing.murmur3_128();

    @BeforeClass
    public static void before() {
        caster = getCaster(CASTER_CLUSTER_ADDRESS);
    }

    public static Caster getCaster(String address){

        CasterProperties params = new CasterProperties();
        params.clusterAddress(address);
        params.clusterName("redis-similar-count-test");
        params.clusterPasswd("");
        params.setProperty(CasterProperties.CASTER_REDIS_CODEC, StringCodec.class.getName());
        params.setProperty(CasterProperties.NODE_CONNECTION_MIN_SIZE, "1");
        return CasterBuilder.build(params);
    }

    @AfterClass
    public static void after() throws Exception {
        if (caster != null) {
            caster.close();
        }
    }

//    @Test
    public void testSet() {
        Caster oldCaster = getCaster("**********:6780");
        CasterSet<String> oldSet = oldCaster.getOrCreateSet("12:zhcc7dfa6f-b3ad-492e-80d1-0e725f441fab");
        CasterSet<Integer> set  = caster.getOrCreateSet("12-test");
        for (String s : oldSet) {
            int number = MURMUR.hashString(s, StandardCharsets.UTF_8).asInt();
            set.add(number);
        }
    }

    @Test
    public void testSimilarTTL() {
        long createdAt = 1636017120000L;
        long ttl = getExpiredAt(createdAt) - System.currentTimeMillis();
        System.out.println("ttl = " + ttl);
    }

    /**
     * 根据创建时间获取过期时间
     * 在n月后的第一天过期
     *
     * @param createdAt
     * @return 过期时间
     */
    private long getExpiredAt(long createdAt) {
        ZoneId ZONE_ID = TimeZone.getTimeZone("GMT+8:00").toZoneId();
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(createdAt), ZONE_ID)
                            .plusMonths(3)
                            .with(TemporalAdjusters.firstDayOfMonth())
                            .atZone(ZONE_ID).toInstant().toEpochMilli();
    }
}
