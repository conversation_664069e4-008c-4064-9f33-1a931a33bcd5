/*
 * Copyright © 2020 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2020湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.blogger.export;

import com.antfact.commons.v2.ProtoBufFormatter;
import com.antfact.commons.v2.model.alarm.AlarmPack;
import com.antfact.commons.v2.model.alarm.AlarmRuleEvent;

import com.antdu.freon.commons.entity.FreonCommons;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <p>buka 发数据测试 </p>
 * <p>
 * 创建日期： 2020/12/22
 *
 * <AUTHOR> tiantian(<EMAIL>)
 */
public class BukaTest {
    /**
     * kafka生产者
     */
    private KafkaProducer producer;
    /**
     * buka主题
     */
    private String topic = "massevent.alarmrule.test";
    private String bukaHosts = "yu-buka1:9096";

    @Before
    public void before() {
        Map<String, Object> params = new HashMap<>();
        params.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bukaHosts);
        params.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        params.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class);
        producer = new KafkaProducer(params);
    }

    @After
    public void after() {
        try {
            producer.flush();
        } catch (Throwable e) {
            throw new RuntimeException("BukaRecordWriter close 出错", e);
        } finally {
            producer.close();
        }
    }

    @Test
    public void getMessage() throws Exception {
        Properties properties = new Properties();

        //buka相关配置
        properties.put(FreonCommons.Buka.BOOTSTARP_SERVERS, bukaHosts);
        properties.put(FreonCommons.Buka.KEY_DESERIALIZER, ByteArrayDeserializer.class);
        properties.put(FreonCommons.Buka.VALUE_DESERIALIZER, ByteArrayDeserializer.class);
        properties.put(FreonCommons.Buka.GROUP_ID, "buka-test-2");
        properties.put(FreonCommons.Buka.ENABLE_AUTO_COMMIT_CONFIG, "true");
        properties.put(FreonCommons.Buka.AUTO_OFFSET_RESET, "latest");
        KafkaConsumer<byte[], byte[]> consumer = new KafkaConsumer<>(properties);
        consumer.subscribe(Collections.singleton("massevent.alarmrule.events"));
        while (true) {
            ConsumerRecords<byte[], byte[]> poll = consumer.poll(100);
            if (poll == null) {
                continue;
            }
            for (ConsumerRecord<byte[], byte[]> consumerRecord : poll) {

                byte[] value = consumerRecord.value();
                AlarmRuleEvent alarmRule = ProtoBufFormatter.fromByteArray(AlarmRuleEvent.class, value);
                String type = alarmRule.hasAlarmRuleDeleted() ? "delete" : "create";
                System.out.println(type + " " + alarmRule.getTopicId());

            }
        }

    }

    @Test
    public void testSend() {
        //发送userid
        AlarmPack.AlarmRule rule = AlarmPack.AlarmRule.newBuilder()
                                                      .setAlarmRuleId("test")
                                                      .setUserId("test")
                                                      .setTopicId("test")
                                                      .setClientId("test")
                                                      .setTopicAuthorCount(30)
                                                      .addAlarmType(AlarmPack.AlarmType.MASS_EVENT)
                                                      .build();
        producer.send(new ProducerRecord(topic, "", rule.toByteArray()));
    }
}
