/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.storage.caster.service;

import com.antdu.freon.cache.ReaderCache;
import com.antdu.freon.cache.WriterCache;
import com.antdu.freon.commons.commons.Commons;
import com.antdu.freon.commons.utils.ClockUtils;
import com.antdu.freon.storage.caster.exception.CasterPersistException;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>caster实现的持久化服务</p>
 * <p/>
 * 创建日期 2019/5/7
 *
 * @param <T> value类型
 * <AUTHOR> pingzhu(<EMAIL>)
 * <AUTHOR> hui(<EMAIL>)
 * @since 1.0.0
 */
@Slf4j
@Component
public class CasterPersistenceService<T> {

    /**
     * 日志输出的最大延迟时间，超过这个时间会输出一次日志
     */
    @Value("${log.print.interval:5000}")
    private long interval;

    /**
     * caster streamCheck
     */
    @Autowired(required = false)
    private CasterStreamCheck casterStreamCheck;

    /**
     * 最新一次输出日志的时间，用于控制日志的输出频率
     */
    private long overdue = ClockUtils.now();

    /**
     * 注入写缓存持久器
     */
    @Autowired
    private WriterCache writerCache;

    /**
     * 实体主键名
     */
    @Value("${freon.entity.field.primaryKey}")
    private String primaryKey;

    /**
     * message 字段映射bean
     */
    @Autowired
    private Map<String, Descriptors.FieldDescriptor> descriptorMap;

    /**
     * protobuf 平台字段
     */
    @Value("${freon.entity.field.platform:}")
    private String platform;

    /**
     * protobuf 用户id字段
     */
    @Value("${freon.entity.field.userId:}")
    private String userId;

    /**
     * protobuf 删读缓存判断字段
     */
    @Value("${freon.entity.field.delCache:}")
    protected String delCacheField;

    /**
     * 注入读缓存持久器
     */
    @Autowired
    private ReaderCache readerCache;

    /**
     * caster持久方法
     *
     * @param msgs caster持久数据集合
     */
    @SuppressWarnings("unchecked")
    public void casterPersistence(Collection<? extends T> msgs) {
        if (null == msgs || msgs.isEmpty()) {
            return;
        }
        long begin = ClockUtils.now();

        //数据如果配置了platform 和userid,但是message中没有值，则过滤
        if (StringUtils.isNotBlank(platform) && StringUtils.isNotBlank(userId)) {
            msgs = msgs.stream()
                       .filter(t -> StringUtils.isNotBlank((String) ((Message) t).getField(descriptorMap.get(platform)))
                           && StringUtils.isNotBlank((String) ((Message) t).getField(descriptorMap.get(userId))))
                       .collect(Collectors.toSet());
        }

        try {
            writerCache.persist(msgs);   //存入写缓存
        } catch (Exception e) {
            log.error("caster persistence error!", e);
            throw new CasterPersistException("caster persistence error!", e);
        }

        if (ClockUtils.now() - overdue >= interval) {
            overdue = ClockUtils.now();
            log.info("the size of the write cache collection:{},cost:{}ms",
                     msgs.size(), (ClockUtils.now() - begin));
        }

        if (null != casterStreamCheck) {    //是否发送streamcheck
            casterStreamCheck.getSendDataSets()
                             .addAll(msgs.stream()
                                         .map(t -> (String) ((Message) t).getField(descriptorMap.get(primaryKey)))
                                         .filter(k -> k.startsWith(Commons.PRE_DPC_TEST))
                                         .collect(Collectors.toSet()));
        }
    }

    /**
     * 删除caster持久方法
     *
     * @param msgs 删除caster持久数据集合
     */
    public void clearCachePersistence(Collection<? extends T> msgs) {
        if (null == msgs || msgs.isEmpty()) {
            return;
        }

        //读缓存为空，或没有删读缓存判断字段值，则不删除
        if (readerCache == null || StringUtils.isBlank(delCacheField)) {
            return;
        }
        //有删读缓存判断字段值
        msgs.forEach(c -> {
            Object del = ((Message) c).getField(descriptorMap.get(delCacheField));
            //字段值为true，则删除读缓存，否则不删除
            if (del instanceof Boolean && (Boolean) del) {
                delReadCache(c);
            }
        });
    }

    /**
     * 删除读缓存
     *
     * @param t
     */
    protected void delReadCache(T t) {
        if (readerCache == null) {
            return;
        }
        Object key = ((Message) t).getField(descriptorMap.get(primaryKey));
        boolean remove = readerCache.remove(key);
        if (log.isDebugEnabled() && remove) {
            log.debug("remove from readerCache, key:{}", key);
        }
    }

}
