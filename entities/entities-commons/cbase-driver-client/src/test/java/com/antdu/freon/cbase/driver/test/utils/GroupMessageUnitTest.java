/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.cbase.driver.test.utils;

import com.antdu.freon.cbase.driverclient.dto.GroupMessage;
import com.antdu.freon.commons.utils.JsonUtil;

import org.junit.Assert;
import org.junit.Test;

/**
 * <p>群消息实体单元测试</p>
 * <p/>
 * 创建日期 2020/9/18
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
public class GroupMessageUnitTest {

    @Test
    public void test() {
        String textMsgJson = "{\"account\":\"***********\",\"data\":{\"content\":{\"type\":\"text\",\"text\":\"哈哈\"},\"fromGroup\":\"***********@chatroom\",\"fromUser\":\"xiangnanlsy\",\"msgId\":**********,\"msgSource\":\"<msgsource> <silence > 0 < /silence> <membercount > 4 < /membercount> </msgsource>\",\"newMsgId\":8294188644242911765,\"self\":false,\"timestamp\":**********,\"toUser\":\"wxid_7pzdb943wghb22\",\"wId\":\"8e966829-6bb0-4f12-8050-1f6aac9220fd\"},\"messageType\":9,\"wcId\":\"wxid_7pzdb943wghb22\"}";
        String imgMsgJson = "{\"account\":\"***********\",\"data\":{\"fromGroup\":\"***********@chatroom\",\"fromUser\":\"xiangnanlsy\",\"img\":\"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\",\"msgId\":**********,\"newMsgId\":2780225466185141525,\"self\":false,\"timestamp\":**********,\"toUser\":\"wxid_7pzdb943wghb22\",\"wId\":\"4139869d-9ead-4c44-b8f3-40a7429a3a59\",\"content\":{\"url\":\"http://xundaokeji.oss-cn-beijing.aliyuncs.com/********/3ed2401d-abf3-4217-8d48-da4918163ac3.png?Expires=**********&OSSAccessKeyId=LTAI4G5VB9BMxMDV14c6USjt&Signature=lrbenPSSF1v%2BOmP1HyPZDFJfyPw%3D\",\"type\":\"img\"}},\"messageType\":10,\"wcId\":\"wxid_7pzdb943wghb22\"}";
        String voiceMsgJson = "{\"account\":\"***********\",\"data\":{\"fromGroup\":\"***********@chatroom\",\"fromUser\":\"xiangnanlsy\",\"msgId\":**********,\"newMsgId\":1193842303020460421,\"self\":false,\"timestamp\":**********,\"toUser\":\"wxid_7pzdb943wghb22\",\"wId\":\"8e966829-6bb0-4f12-8050-1f6aac9220fd\",\"content\":{\"url\":\"http://xundaokeji.oss-cn-beijing.aliyuncs.com/********/03ea32a9-4bf4-4070-bd52-a5c9237315d7.mp4?Expires=**********&OSSAccessKeyId=LTAI4G5VB9BMxMDV14c6USjt&Signature=wfch%2BWYP7nU1SOm6trCg2An3uos%3D\",\"type\":\"vid\"}},\"messageType\":11,\"wcId\":\"wxid_7pzdb943wghb22\"}";

        GroupMessage text = JsonUtil.parseJson(textMsgJson, GroupMessage.class);
        GroupMessage img = JsonUtil.parseJson(imgMsgJson, GroupMessage.class);
        GroupMessage voice = JsonUtil.parseJson(voiceMsgJson, GroupMessage.class);

        Assert.assertEquals("text", text.getData().getContent().getType());
        Assert.assertEquals("img", img.getData().getContent().getType());
        Assert.assertEquals("vid", voice.getData().getContent().getType());
    }

}
