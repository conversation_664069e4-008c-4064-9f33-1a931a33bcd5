apiVersion: v1
kind: Service
metadata:
  name: freon-pure-text-tags-service
spec:
  selector:
    app: freon-pure-text-tags-rest
  ports:
    - name: rest-port
      port: 8395
      targetPort: 8395
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-pure-text-tags-rest
  namespace:
  labels:
    app: freon-pure-text-tags-rest
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-pure-text-tags-rest
  template:
    metadata:
      labels:
        app: freon-pure-text-tags-rest
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-pure-text-tags-rest
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-pure-text-tags.yml,application-commons.yml"
            - "-Dapp_name=freon-pure-text-tags-rest"
            - "-Droute.rest=true"
            - "-Dcbase.clusters.map.hot_cluster.enable=true"
            - "-Dcbase.clusters.map.cold_cluster.enable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          ports:
            - name: rest-port
              containerPort: 8395
          readinessProbe:
            httpGet:
              path: /pure-text-tags/1
              port: 8395
            initialDelaySeconds: 60
          volumeMounts:
            - name: rest-config
              mountPath: /home/<USER>
      volumes:
        - name: rest-config
          configMap:
            name: freon-message-entities-cm

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-pure-text-tags-cbase-hot
  namespace:
  labels:
    app: freon-pure-text-tags-cbase-hot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-pure-text-tags-cbase-hot
  template:
    metadata:
      labels:
        app: freon-pure-text-tags-cbase-hot
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-pure-text-tags-cbase-hot
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-pure-text-tags.yml,application-commons.yml"
            - "-Dapp_name=freon-tags-cbase-hot"
            - "-Dproperties.storage.jobs.cbase_hot.runnable=true"
            - "-Dcbase.clusters.map.hot_cluster.enable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: cbase-hot-config
              mountPath: /home/<USER>
      volumes:
        - name: cbase-hot-config
          configMap:
            name: freon-message-entities-cm

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-pure-text-tags-cbase-cold
  namespace:
  labels:
    app: freon-pure-text-tags-cbase-cold
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-pure-text-tags-cbase-cold
  template:
    metadata:
      labels:
        app: freon-pure-text-tags-cbase-cold
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-pure-text-tags-cbase-cold
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-pure-text-tags.yml,application-commons.yml"
            - "-Dapp_name=freon-tags-cbase-cold"
            - "-Dproperties.storage.jobs.cbase_cold.runnable=true"
            - "-Dcbase.clusters.map.cold_cluster.enable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: cbase-cold-config
              mountPath: /home/<USER>
      volumes:
        - name: cbase-cold-config
          configMap:
            name: freon-message-entities-cm
