server:
  port: 8591

route:
  rest: false
  path: short-video

spring:
  application:
    name: freon-message-similar-shortvideo

antdu:
  metrics:
    clusterName: freon-message-similar-shortvideo

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.skinny.similardoc.SimilarDocStorage
    classpath: com.antfact.commons.v2.model.StatusPack$WeMedia
    ignoreTimeStampSuffix: false
    field:
      primaryKey: wmId
      createat: createdAt
      delCache: important

properties:
  storage:
    buka:
      producerTopic: message-similar-shortvideo-topic
    jobs:
      caster:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.storage.caster.storage.similardoc.SimilarCasterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 3
            topics:
              - wemedia_similarity_checked
            group: message-similar-shortvideo-group-caster-new
            persistId: persistence-message-similar-shortvideo-caster
            persistLimitSpeed: 50000
          deflect:
            threadCount: 3
            topic: message-similar-shortvideo-topic-deflect-caster
            group: message-similar-shortvideo-group-deflect-caster
            persistId: persistence-message-similar-shortvideo-caster-deflect
            persistLimitSpeed: 50000
          failback:
            threadCount: 1
            topic: message-similar-shortvideo-topic-failback-caster
            group: message-similar-shortvideo-group-failback-caster
            persistId: persistence-message-similar-shortvideo-caster-failback
            persistLimitSpeed: 30000
      cbase_hot:
        runnable: false
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.similardoc.SimilarDocStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_similarity_checked
            group: message-short-video-group-cbase-hot
            persistId: persistence-message-short-video-cbase-hot
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-short-wedeo-topic-deflect-cbase-hot
            group: message-short-video-group-deflect-cbase-hot
            persistId: persistence-message-short-video-cbase-hot-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-short-wedeo-topic-failback-cbase-hot
            group: message-short-video-group-failback-cbase-hot
            persistId: persistence-message-short-video-cbase-hot-failback
            persistLimitSpeed: 30000

cbase:
  keyspace: yuqing_twcs
  table: doc_view
  clusters:
    map:
      hot_cluster:
        enable: false
        clusterNodes: 10.20.2.43,10.20.2.44,10.20.2.45,10.20.2.46,10.20.2.47
        clusterName: yu-hot-cluster
        port: 9042
        datacenter: datacenter1
        username: cbase
        password: antducbaseadmin@2022
        operateTimeout: 30000
        ttlMaxDays: 14
        subTableDuration: 604800
        ttlMax: 8035200
        ttlMin: 604800
        compactType: twcs
        sequenceNo: 0

cache:
  name_pre: message.view

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: short-video-reader-ratelimiter

persist:
  executor:
    max_thread: 12

readCache.enable: false
writeCache.enable: false

caster:
  enable: false
  clusterName: redis-similar-data-store
  clusterAddress: ***********:8000,***********:8000,***********:8000,***********:8000