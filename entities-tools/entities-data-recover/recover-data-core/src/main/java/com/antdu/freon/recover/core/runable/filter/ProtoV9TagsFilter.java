/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.recover.core.runable.filter;

import com.antfact.commons.v2.model.StatusPack;
import com.antfact.mapreduce.google.protobuf.Descriptors;
import com.antfact.mapreduce.google.protobuf.Message;

import com.google.common.collect.Sets;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;

/**
 * 创建日期 2021/7/28
 * tags filter
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Component("protoV9TagsFilter")
@ConditionalOnExpression("#{environment['generic.filter'] != null && environment['generic.filter'].contains('protoV9TagsFilter')}")
@SuppressWarnings({"checkstyle:MagicNumber", "lineLength"})
public class ProtoV9TagsFilter extends AbsSourceFilter<Message> {

    /**
     * 实体字段详情 映射
     */
    @Autowired
    private Map<String, Descriptors.FieldDescriptor> descriptorMap;
    /**
     * tag字段
     */
    private Descriptors.FieldDescriptor tagsField;
    /**
     * 需要过滤字段
     */
    @Value("${recover.tagValue:}")
    private String filterTagValue;
    /**
     * init
     */
    @PostConstruct
    public void init() {
        tagsField = descriptorMap.get("tags");
    }

    @Override
    public boolean test(Message message) {
        StatusPack.Tags tag = (StatusPack.Tags) message.getField(tagsField);
        Set<String> tagsSet = new HashSet();
        tagsSet.addAll(Sets.newHashSet(tag.getTagLocation().trim().split(" ")));
        tagsSet.addAll(Sets.newHashSet(tag.getTagEmotion().trim().split(" ")));
        tagsSet.addAll(Sets.newHashSet(tag.getTagIndustry().trim().split(" ")));
        tagsSet.addAll(Sets.newHashSet(tag.getTagDomain().trim().split(" ")));
        tagsSet.addAll(Sets.newHashSet(tag.getTagTopic().trim().split(" ")));
        tagsSet.addAll(Sets.newHashSet(tag.getTagSimilar().trim().split(" ")));
        tagsSet.addAll(Sets.newHashSet(tag.getTagOther().trim().split(" ")));
        return  !tagsSet.contains(filterTagValue);
    }
}
