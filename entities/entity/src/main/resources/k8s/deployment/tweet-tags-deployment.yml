apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-tweet-tags-caster
  namespace:
  labels:
    app: freon-tweet-tags-caster
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-tweet-tags-caster
  template:
    metadata:
      labels:
        app: freon-tweet-tags-caster
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-tweet-tags-caster
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-tweet-tags.yml,application-commons.yml"
            - "-Dapp_name=freon-tags-caster"
            - "-Dproperties.storage.jobs.caster.runnable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: caster-config
              mountPath: /home/<USER>
      volumes:
        - name: caster-config
          configMap:
            name: freon-message-entities-cm

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-tweet-tags-cbase-hot
  namespace:
  labels:
    app: freon-tweet-tags-cbase-hot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-tweet-tags-cbase-hot
  template:
    metadata:
      labels:
        app: freon-tweet-tags-cbase-hot
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-tweet-tags-cbase-hot
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-tweet-tags.yml,application-commons.yml"
            - "-Dapp_name=freon-tags-cbase-hot"
            - "-Dproperties.storage.jobs.cbase_hot.runnable=true"
            - "-Dcbase.clusters.map.hot_cluster.enable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: cbase-hot-config
              mountPath: /home/<USER>
      volumes:
        - name: cbase-hot-config
          configMap:
            name: freon-message-entities-cm

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-tweet-tags-cbase-original
  namespace:
  labels:
    app: freon-tweet-tags-cbase-original
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-tweet-tags-cbase-original
  template:
    metadata:
      labels:
        app: freon-tweet-tags-cbase-original
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-tweet-tags-cbase-original
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-tweet-tags.yml,application-commons.yml"
            - "-Dapp_name=freon-tags-cbase-original"
            - "-Dproperties.storage.jobs.cbase_original.runnable=true"
            - "-Dcbase.clusters.map.original_cluster.enable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: cbase-original-config
              mountPath: /home/<USER>
      volumes:
        - name: cbase-original-config
          configMap:
            name: freon-message-entities-cm

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-tweet-tags-cbase-cold
  namespace:
  labels:
    app: freon-tweet-tags-cbase-cold
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-tweet-tags-cbase-cold
  template:
    metadata:
      labels:
        app: freon-tweet-tags-cbase-cold
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-tweet-tags-cbase-cold
          image: docker.antfact.com/platform/freon-entities-entity:1.0.1-SNAPSHOT
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-tweet-tags.yml,application-commons.yml"
            - "-Dapp_name=freon-tags-cbase-cold"
            - "-Dproperties.storage.jobs.cbase_cold.runnable=true"
            - "-Dcbase.clusters.map.cold_cluster.enable=true"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          volumeMounts:
            - name: cbase-cold-config
              mountPath: /home/<USER>
      volumes:
        - name: cbase-cold-config
          configMap:
            name: freon-message-entities-cm
