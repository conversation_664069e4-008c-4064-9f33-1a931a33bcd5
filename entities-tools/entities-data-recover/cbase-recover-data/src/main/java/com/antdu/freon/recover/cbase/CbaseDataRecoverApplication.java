/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.recover.cbase;

import com.antdu.freon.cbase.driverclient.CbaseCoreAutoConfiguration;
import com.antdu.freon.cbase.driverclient.config.MultipleDataSourceConfiguration;
import com.antdu.freon.cbase.driverclient.core.ExecutorServicePool;
import com.antdu.freon.persistence.PersistenceAutoconfigure;
import com.antdu.freon.storage.base.config.BaseStorageAutoConfigure;
import com.antdu.freon.storage.base.config.FieldDescriptorAutoConfiguration;
import com.antdu.nest.ratelimit.client.RateLimitAutoconfigure;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <p>cbase 数据恢复主程序</p>
 * <p/>
 * 创建日期 2020/5/28
 *
 * <AUTHOR> ping<PERSON>hu(<EMAIL>)
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.antdu.freon.recover.cbase", "com.antdu.freon.recover.core.runable"},
    scanBasePackageClasses = {RateLimitAutoconfigure.class, MultipleDataSourceConfiguration.class,
        ExecutorServicePool.class},
    exclude = {FieldDescriptorAutoConfiguration.class, CbaseCoreAutoConfiguration.class,
        BaseStorageAutoConfigure.class, PersistenceAutoconfigure.class})
public class CbaseDataRecoverApplication {

    public static void main(String[] args) {
        SpringApplication.run(CbaseDataRecoverApplication.class, args);
    }

}
