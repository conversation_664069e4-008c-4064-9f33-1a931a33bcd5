package com.antdu.freon.search;

import com.google.common.collect.Lists;

import org.apache.http.HttpHost;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.Node;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 创建日期 2022/4/24
 * 数据从索引导出，存放到本地文件
 *
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Slf4j
public class FileDeal {
    public static void main(String... args) throws Exception {
        String query = "bloggerName:";
        String filePath = "/Users/<USER>/Downloads/weixin-hao2";
        BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/Downloads/weixin-hao1"));
        BufferedWriter write = new BufferedWriter(new FileWriter(filePath));
        List<String> list = new ArrayList<>();
        String str = null;
        while ((str = reader.readLine()) != null) {
            list.add("platform:twitter && bloggerNameFix:\"" + str.replace("@","")+"\"");
        }
        reader.close();
        for (String s : list) {
            write.write(s);
            write.newLine();
        }
        write.flush();
        write.close();
    }
}
