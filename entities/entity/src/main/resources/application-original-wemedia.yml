server:
  port: 8592

route:
  rest: false
  path: wemedia

spring:
  application:
    name: freon-message-original-wemedia

antdu:
  metrics:
    clusterName: freon-message-original-wemedia

freon:
  entity:
    classpath: com.antfact.commons.v2.model.StatusPack$WeMedia
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: wmId,contentId,platform,url,title,createdAt,content,userId,userName,nickName,userDescription,headUrl,vip,vipType,picUrl,videoUrl,audioUrl,mediaType,wmType,scope,channelId,channelName,countryCode,domain,expand,ip,ipLocation,latitude,longitude,location,originalContent,originalId,originalUserId,originalUserName,source,websiteName,tagsWithScore,important,authInfo,geoLocation,translation,ocrText,mcRelated,ocrPosition
    ignoreTimeStampSuffix: true
    checkPropsFieldName: props  #易变属性字段
    field:
      primaryKey: wmId
      stateType: wmType
      createat: createdAt
      delCache: important

properties:
  storage:
    buka:
      producerTopic: message-origin-status-topic
    jobs:
      cbase_hot:
        runnable: true
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.special.OriginalDataSkinnyStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedias
            group: message-original-wemedia-group-cbase-hot-new1
            persistId: persistence-message-original-wemedia-cbase-hot
            persistLimitSpeed: 10000
          deflect:
            threadCount: 3
            topic: message-original-wemedia-topic-deflect-cbase-hot
            group: message-original-wemedia-group-deflect-cbase-hot
            persistId: persistence-message-original-wemedia-cbase-hot-deflect
            persistLimitSpeed: 10000
          failback:
            threadCount: 3
            topic: message-original-wemedia-topic-failback-cbase-hot
            group: message-original-wemedia-group-failback-cbase-hot
            persistId: persistence-message-original-wemedia-cbase-hot-failback
            persistLimitSpeed: 10000

cbase:
  keyspace: yuqing_skinny
  table: wemedia
  clusters:
    map:
      hot_cluster:
        enable: true
        clusterNodes: 10.20.2.43,10.20.2.44,10.20.2.45,10.20.2.46,10.20.2.47
        clusterName: yu-hot-cluster
        port: 9042
        datacenter: datacenter1
        username: cassandra
        password: cassandra
        operateTimeout: 30000
        ttlMaxDays: 7
        ttlMax: 604800
        ttlMin: 172800
        sequenceNo: 0

cache:
  name_pre: message.original.wemedia.

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: wemedia-reader-ratelimiter

persist:
  executor:
    max_thread: 12