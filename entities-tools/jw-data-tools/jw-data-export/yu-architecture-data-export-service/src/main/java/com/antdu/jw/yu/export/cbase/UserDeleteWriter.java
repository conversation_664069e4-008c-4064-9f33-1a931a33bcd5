/*
 * Copyright © 2022 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2022湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.jw.yu.export.cbase;

import com.antdu.jw.export.core.cbase.CbaseRowDeleteWriter;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>cbase 根据id删除用户 </p>
 * <p>
 * 创建日期： 2022/10/18
 *
 * <AUTHOR> tiantian(<EMAIL>)
 */
@Slf4j
@Component
@Profile("delete-user-by-userid")
public class UserDeleteWriter extends CbaseRowDeleteWriter {

    /**
     * 集群名
     */
    @Value("${export.cluster.user}")
    private String cluster;

    /**
     * 主键名
     */
    @Value("${export.primaryKeyName.user}")
    private String primaryKeyName;

    /**
     * 命名空间
     */
    @Value("${export.keyspace.user}")
    private String keyspace;

    /**
     * 表名
     */
    @Value("${export.table.user}")
    private String table;

    /**
     * 执行cql
     */
    private String executeCql;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        executeCql = getDeleteCql(keyspace, table, primaryKeyName);
    }

    @Override
    public void write(Set<String> keys) {
        delete(cluster, s -> executeCql, keys);
        printInfo(keys.size());
    }

}
