server:
  port: 8944

route:
  rest: false
  path: mutaprop

spring:
  application:
    name: freon-message-webpage-mutaprop

antdu:
  metrics:
    clusterName: freon-message-webpage-mutaprop

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.skinny.special.MutabilityPropertiesStorage
    classpath: com.antfact.commons.v2.model.StatusPack$MutabilityProperties
    messageFields:  #固定字段 / 扩展字段，逗号分格
      fixed: statusId,repostsCount,commentsCount,state,favourCount,indexedAt,crawlTime,crawler,createAt,source,followerCount
    field:
      stateType: statusType
      primaryKey: statusId
      createat: createAt

properties:
  storage:
    buka:
      producerTopic: message-webpage-mutaprop-topic
    jobs:
      cbase_hot:
        runnable: true
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.gaia.cbase.persist.general.skinny.special.ignore.IgnoreTimeStampTagsStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - props-data-buchong-0727-web
            group: message-webpage-mutaprop-group-cbase-hot
            persistId: persistence-message-webpage-mutaprop-cbase-hot
            persistLimitSpeed: 10000
          deflect:
            threadCount: 3
            topic: message-webpage-mutaprop-topic-deflect-cbase-hot
            group: message-webpage-mutaprop-group-deflect-cbase-hot
            persistId: persistence-message-webpage-mutaprop-cbase-hot-deflect
            persistLimitSpeed: 10000
          failback:
            threadCount: 3
            topic: message-webpage-mutaprop-topic-failback-cbase-hot
            group: message-webpage-mutaprop-group-failback-cbase-hot
            persistId: persistence-message-webpage-mutaprop-cbase-hot-failback
            persistLimitSpeed: 10000
      cbase_cold:
        runnable: false
        pollIsBatch: true
        acknowledgement: true
        className: com.antdu.gaia.cbase.persist.general.skinny.SkinnyRowStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - webpage_property_dedupped
            group: message-webpage-mutaprop-group-cbase-cold
            persistId: persistence-message-webpage-mutaprop-cbase-cold
            persistLimitSpeed: 10000
          deflect:
            threadCount: 3
            topic: message-webpage-mutaprop-topic-deflect-cbase-cold
            group: message-webpage-mutaprop-group-deflect-cbase-cold
            persistId: persistence-message-webpage-mutaprop-cbase-cold-deflect
            persistLimitSpeed: 10000
          failback:
            threadCount: 3
            topic: message-webpage-mutaprop-topic-failback-cbase-cold
            group: message-webpage-mutaprop-group-failback-cbase-cold
            persistId: persistence-message-webpage-mutaprop-cbase-cold-failback
            persistLimitSpeed: 10000
      caster:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.storage.caster.storage.GeneralCasterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - webpage_property_dedupped
            group: message-webpage-mutaprop-group-caster-dev
            persistId: persistence-message-webpage-mutaprop-caster
            persistLimitSpeed: 10000
          deflect:
            threadCount: 6
            topic: message-webpage-mutaprop-topic-deflect-caster
            group: message-webpage-mutaprop-group-deflect-caster-dev
            persistId: persistence-message-webpage-mutaprop-caster-deflect
            persistLimitSpeed: 10000

cbase:
  keyspace: yuqing_skinny
  table: mutability_properties_status
  clusters:
    map:
      hot_cluster:
        enable: true
        clusterNodes: 10.20.2.22,10.20.2.23
        clusterName: Hot Cluster
        port: 9042
        datacenter: cs01
        username: cassandra
        password: cassandra
        operateTimeout: 60000
        ttlMaxDays: 2
        ttlMax: 172800
        ttlMin: 3600
        sequenceNo: 0
      original_cluster:
        enable: false
        clusterNodes: 10.20.2.22,10.20.2.23
        clusterName: Hot Cluster
        port: 9042
        datacenter: cs01
        username: cassandra
        password: cassandra
        table: property_tags_original
        operateTimeout: 60000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 86400
        sequenceNo: 1
      cold_cluster:
        enable: true
        clusterNodes: 10.20.1.108,10.20.1.117,10.20.1.120,10.20.1.123
        clusterName: property-tags-cluster-cold
        port: 9042
        datacenter: dc-opinion
        username: cassandra
        password: cassandra
        operateTimeout: 60000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 86400
        keyHasTimeStamp: true
        sequenceNo: 3

cache:
  name_pre: message.mutaprop.

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: webpage-mutaprop-reader-ratelimiter

persist:
  executor:
    max_thread: 12