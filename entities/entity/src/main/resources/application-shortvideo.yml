server:
  port: 8396

route:
  rest: false
  path: shortvideo

spring:
  application:
    name: freon-message-shortvideo

antdu:
  metrics:
    clusterName: freon-message-shortvideo

freon:
  entity:
    readerStorageClassPath: com.antdu.freon.cbase.driverclient.persist.skinny.SkinnyRowStorage
    classpath: com.antfact.commons.v2.model.StatusPack$WeMedia
    ignoreTimeStampSuffix: true
    field:
      primaryKey: wmId
      createat: createdAt
      delCache: important
    filter:
      mediaType: SHORT_VIDEO

properties:
  storage:
    buka:
      producerTopic: message-shortvideo-topic
    jobs:
      cbase_hot:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.special.WemediaTypeFilterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_similarity_checked
            group: message-shortvideo-group-cbase-hot
            persistId: persistence-message-wemedia-shortvideo-cbase-hot
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-shortvideo-topic-deflect-cbase-hot
            group: message-shortvideo-group-deflect-cbase-hot
            persistId: persistence-message-shortvideo-cbase-hot-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-shortvideo-topic-failback-cbase-hot
            group: message-shortvideo-group-failback-cbase-hot
            persistId: persistence-message-shortvideo-cbase-hot-failback
            persistLimitSpeed: 30000
      cbase_cold:
        runnable: false
        pollIsBatch: true
        className: com.antdu.freon.cbase.driverclient.persist.skinny.special.WemediaTypeFilterStorage
        mode: poll
        consumers:
          normal:
            threadCount: 6
            topics:
              - wemedia_similarity_checked
            group: message-shortvideo-group-cbase-cold
            persistId: persistence-message-cbase-cold
            persistLimitSpeed: 30000
          deflect:
            threadCount: 3
            topic: message-shortvideo-topic-deflect-cbase-cold
            group: message-shortvideo-group-deflect-cbase-cold
            persistId: persistence-message-shortvideo-cbase-cold-deflect
            persistLimitSpeed: 30000
          failback:
            threadCount: 3
            topic: message-shortvideo-topic-failback-cbase-cold
            group: message-shortvideo-group-failback-cbase-cold
            persistId: persistence-message-shortvideo-cbase-cold-failback
            persistLimitSpeed: 30000

cbase:
  keyspace: yuqing_skinny
  table: shortvideo
  clusters:
    map:
      hot_cluster:
        enable: false
        clusterNodes: 10.20.2.43,10.20.2.44,10.20.2.45,10.20.2.46,10.20.2.47
        clusterName: yu-hot-cluster
        port: 9042
        datacenter: datacenter1
        username: cassandra
        password: cassandra
        operateTimeout: 60000
        ttlMaxDays: 2
        ttlMax: 172800
        ttlMin: 3600
        sequenceNo: 0
      cold_cluster:
        enable: false
        clusterNodes: 10.20.5.9,10.20.5.10,10.20.5.20,10.20.5.25,10.20.5.28
        clusterName: opinion-cold-cluster
        port: 9042
        datacenter: dc-opinion
        username: cassandra
        password: cassandra
        operateTimeout: 60000
        ttlMaxDays: 93
        ttlMax: 8035200
        ttlMin: 86400
        sequenceNo: 1

cache:
  name_pre: message.shortvideo

reader:
  ratelimit:
    timeSecond: 1
    capacity: 10000
    resourceId: shortvideo-reader-ratelimiter

persist:
  executor:
    max_thread: 12