/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.recover.file.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


import lombok.Data;

/**
 * 创建日期 2021/8/13
 * 文件配置类
 * <AUTHOR> @ antdu.com)
 * @since 1.0.0
 */
@Configuration
@Data
public class FileOutConfig {
    /**
     * 多文件方式 true/false
     */
    @Value("${file.store.mulFile:false}")
    private boolean mulFile;
    /**
     * 多少数据分割 文件 不是严谨意义上的
     */
    @Value("${file.store.mulFileSize:10000}")
    private long fileSize;
    /**
     * batch flush File
     */
    @Value("${file.store.batchSize:100}")
    private long fileBatchSize;
    /**
     * 输出路径
     */
    @Value("${file.store.outputPath:/home/<USER>")
    private String outputPath;
    /**
     * fileName
     */
    @Value("${file.store.fileName:web}")
    private String fileName;
    /**
     * format Type
     */
    @Value("${file.store.formatType:web}")
    private String dataFormatType;
}
