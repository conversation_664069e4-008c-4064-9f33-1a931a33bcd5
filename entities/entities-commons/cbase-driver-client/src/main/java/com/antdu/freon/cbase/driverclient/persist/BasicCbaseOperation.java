/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.cbase.driverclient.persist;

import com.antdu.freon.cache.ReaderCache;
import com.antdu.freon.cache.WriterCache;
import com.antdu.freon.cbase.driverclient.core.CqlSessionWrapper;
import com.antdu.freon.cbase.driverclient.core.ExecutorServicePool;
import com.antdu.freon.cbase.driverclient.dto.SingleExecutePersistDTO;
import com.antdu.freon.commons.utils.ClockUtils;
import com.antdu.freon.commons.utils.RestUtils;
import com.antdu.freon.spi.AcknowledgeHandler;
import com.antdu.freon.utils.MetricsUtils;
import com.codahale.metrics.Timer;
import com.datastax.oss.driver.api.core.ConsistencyLevel;
import com.datastax.oss.driver.api.core.CqlSession;
import com.datastax.oss.driver.api.core.cql.AsyncResultSet;
import com.datastax.oss.driver.api.core.cql.BoundStatement;
import com.datastax.oss.driver.api.core.cql.PreparedStatement;
import com.datastax.oss.driver.api.core.cql.Row;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>cassandra 基础操作 基类封装</p>
 * <p/>
 * 创建日期 2020/10/23
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Slf4j
@Component
public class BasicCbaseOperation {

    /**
     * 最新一次输出日志的时间，用于控制日志的输出频率
     */
    protected long overdue = ClockUtils.now();

    /**
     * cbase keyspace名
     */
    @Value("${cbase.keyspace}")
    protected String keyspace;

    /**
     * cbase表名
     */
    @Value("${cbase.table}")
    protected String table;

    /**
     * cbase熔断器超时时间(毫秒) 默认3秒
     */
    @Value("${cbase.circuitbreaker.timeout:3000}")
    protected long circuitbreakerTimeout;

    /**
     * cbase 多数据源map
     */
    @Resource(name = "cqlSessionWrapperMap")
    protected LinkedHashMap<String, CqlSessionWrapper> sessionPoolMap;

    /**
     * prestatement Map
     */
    private Map<String, PreparedStatement> preStatMap = Maps.newConcurrentMap();

    /**
     * 日志输出的最大延迟时间，超过这个时间会输出一次日志
     */
    @Value("${log.print.interval:5000}")
    protected long interval;

    /**
     * 写缓存  (做修改操作是需要清理缓存,非必选)
     */
    @Autowired(required = false)
    protected WriterCache writerCache;

    /**
     * 读缓存  (做修改操作是需要清理缓存,非必选)
     */
    @Autowired(required = false)
    protected ReaderCache readerCache;

    /**
     * 自定义线程池
     */
    @Autowired
    protected ExecutorServicePool executorServicePool;

    /**
     * resultsetfuture 与 查询dto的映射map
     */
    private Map<CompletableFuture, SingleExecutePersistDTO> rsFutureMap = new ConcurrentHashMap<>();

    /**
     * resultSetFuture 执行计数器
     */
    protected LongAdder rsFutureLongAdder = new LongAdder();

    /**
     * 可执行的future回调标识
     */
    private AtomicBoolean runableScheduled = new AtomicBoolean(true);

    /**
     * 执行futrue休眠时间
     */
    @Value("${future.sleep.time:500}")
    private long futureSleepTime;

    /**
     * 消费提交个数限制
     */
    @Value("${future.consumer.limit:30000}")
    private Integer consumerSubmitLimit;

    /**
     * 消费提交sleep time
     */
    @Value("${future.consumer.sleep.time:100}")
    private Integer consumerSubmitSleepTime;

    /**
     * 原帖存储最小时间 默认2天
     */
    @Value("${freon.entity.origindata.minttl:172800}")
    protected long originDataMinTTL;

    /**
     * 原帖存储最大时间 默认93天
     */
    @Value("${freon.entity.origindata.maxttl:8035200}")
    protected long originDataMaxTTL;

    /**
     * 记录上一次的存储total数
     */
    protected long lastTimeTotal = 0L;

    /**
     * 热备标识
     */
    protected static final String HOT_CLUSTER = "hot_cluster";

    /**
     * 冷备标识
     */
    protected static final String COLD_CLUSTER = "cold_cluster";

    /**
     * status，property_tags 热备另外存一个keyspace，后期需扩展
     */
    @Value("${cbase.specialKeyspace:ks_original_data}")
    protected String specialKeyspace;

    /**
     * 初始化一个单线程，执行future回调
     */
    @SuppressWarnings({"unchecked", "checkstyle:MagicNumber"})
    protected void initFutureScheduled() {
        Executors.newSingleThreadScheduledExecutor().execute(() -> {
            while (runableScheduled.get()) {
                try {
                    if (rsFutureLongAdder.sum() <= 0) {
                        sleep(futureSleepTime);
                    }
                    rsFutureMap
                        .entrySet()
                        .stream()
                        .filter(entry -> entry.getKey().isDone() || entry.getKey().isCancelled())
                        .forEach(entry -> {
                            SingleExecutePersistDTO dto = entry.getValue();
                            try {
                                entry.getKey().get();
                                dto.getSucAdder().increment();
                                if (ClockUtils.now() - overdue >= interval) {
                                    overdue = ClockUtils.now();
                                    log.info("clustername:{},submission successful!,total:{},suc:{},fail:{},"
                                                 + "qps:{}, callback future size:{}",
                                             dto.getSession().getName(),
                                             dto.getTotalAdder(), dto.getSucAdder(), dto.getErrorAdder(),
                                             (dto.getTotalAdder().sum() - lastTimeTotal) / (interval / 1000L),
                                             rsFutureLongAdder.sum());
                                    lastTimeTotal = dto.getTotalAdder().sum();
                                }

                                if (null != dto.getSendStreamCheckDataFunc()) {   //是否需要发送streamCheck数据
                                    dto.getSendStreamCheckDataFunc().accept(dto.getObj());
                                }

                                if (null != dto.getSendPersistCounterFunc()) {  //是否需要发送持久统计数据
                                    dto.getSendPersistCounterFunc().accept(dto.getObj());
                                }

                            } catch (InterruptedException | ExecutionException e) {
                                log.error("clustername:{}, execute callback save is failed,callback future size:{}",
                                          dto.getSession().getName(), rsFutureLongAdder.sum(), e);
                                dto.getErrorAdder().increment();

                                if (null != dto.getSendFailureDataFunc()) {   //是否需要发送持久失败数据
                                    dto.getSendFailureDataFunc().accept(dto.getObj());
                                }
                            } finally {
                                rsFutureMap.remove(entry.getKey());
                                rsFutureLongAdder.decrement();


                                LongAdder shouldBePerformed = dto.getShouldBePerformed();
                                AcknowledgeHandler handler = dto.getAcknowledgeHandler();

                                if (null != shouldBePerformed && null != handler) {
                                    //一个存储批次的应存储计数减一
                                    shouldBePerformed.decrement();

                                    if (shouldBePerformed.sum() <= 0) {    //若一批次存储完成，即发送ack
                                        handler.acknowledge(dto.getHeadElement());
                                    }
                                }
                            }
                        });
                } catch (Exception e) {
                    log.error("future scheduled callback error", e);
                }
            }
        });
    }

    /**
     * 获取prestatement
     *
     * @param session session
     * @param cql     预处理的cql语句
     * @return
     */
    protected PreparedStatement getPreStatement(CqlSession session, String cql) {
        return preStatMap.computeIfAbsent(
            session.getName() + cql.trim(), func -> session.prepare(cql));
    }

    /**
     * 获取BoundStatement
     *
     * @param session session
     * @param cql     预处理的cql语句
     * @param level   一致性级别
     * @param obj     绑定参数
     * @return
     */
    protected BoundStatement getBoundStatement(CqlSession session, String cql,
                                               ConsistencyLevel level, Object... obj) {

        PreparedStatement preparedStatement = getPreStatement(session, cql);

        //java-driver-core 4.X中需要这样设置读写一致性级别
        BoundStatement boundStatement = preparedStatement.bind(obj);
        boundStatement = boundStatement.setConsistencyLevel(level);
        return boundStatement;
    }

    /**
     * 执行callback
     *
     * @param dto 执行参数dto
     */
    @SuppressWarnings({"Indentation"})
    protected void executeCallback(SingleExecutePersistDTO dto) {
        dto.getTotalAdder().increment();
        if (rsFutureLongAdder.sum() >= consumerSubmitLimit) {
            sleep(consumerSubmitSleepTime);
        }
        rsFutureMap.put(dto.getSession().executeAsync(dto.getStatement()).toCompletableFuture(), dto);
        rsFutureLongAdder.increment();
    }

    /**
     * widerow 异步单条查询 action
     *
     * @param key    主键key
     * @param rowMap widerow批量查Map<String,List<Row>>结果集
     * @param latch  异步计数器
     * @param <K>    主键泛型
     * @return
     */
    protected <K> BiConsumer<AsyncResultSet, Throwable> wideRowGetAllAction(
        K key, Map<K, List<Row>> rowMap, CountDownLatch latch) {

        return (asyncResultSet, throwable) -> {
            try {
                if (throwable != null) {
                    log.error("widerow get all function is error ", throwable);
                    return;
                }
                //widerow单条查询list结果集 widerow一个key对应多行
                List<Row> resultRows = Lists.newArrayList();
                asyncResultSet.currentPage().forEach(resultRows::add);
                rowMap.put(key, resultRows);
            } finally {
                latch.countDown();
            }
        };
    }

    /**
     * skinnyrow 异步单条查询 action
     *
     * @param key    主键
     * @param rowMap 查询结果集
     * @param latch  异步计数器
     * @param <K>    主键泛型
     * @return
     */
    protected <K> BiConsumer<AsyncResultSet, Throwable> skinnyRowGetAllAction(
        K key, Map<K, Row> rowMap, CountDownLatch latch) {
        return skinnyRowGetAllAction(key, rowMap, latch, null);
    }

    /**
     * skinnyrow 异步单条查询 action
     *
     * @param key          主键
     * @param rowMap       查询结果集
     * @param latch        异步计数器
     * @param timerContext timer度量上下文，用于度量查询时间
     * @param <K>          主键泛型
     * @return
     */
    protected <K> BiConsumer<AsyncResultSet, Throwable> skinnyRowGetAllAction(
        K key, Map<K, Row> rowMap, CountDownLatch latch, Timer.Context timerContext) {

        return (asyncResultSet, throwable) -> {
            try {
                if (throwable != null) {
                    log.error("skinnyrow get all function is error ", throwable);
                    return;
                }
                asyncResultSet.currentPage().forEach(r -> rowMap.put(key, r));
            } finally {
                MetricsUtils.stop(timerContext);
                latch.countDown();
            }
        };
    }

    /**
     * 参照 IgnoreTimestampSuffixReader 需要将传参查询的id和实际查询的id做一个转换
     *
     * @param result            查询结果集
     * @param primaryDescriptor 主键protobuf描述对象
     * @param queryKeysMap      key ->去掉时间戳的key value->原查询带时间戳的key
     * @param <K>               主键泛型
     * @param <T>               实体泛型
     * @return 转换结果
     */
    @SuppressWarnings("unchecked")
    protected <K, T> Map<K, T> dataConvert(Map<K, T> result, Descriptors.FieldDescriptor primaryDescriptor,
                                           Map<Object, String> queryKeysMap) {
        return (Map<K, T>) result.values().stream().map(t -> {
            Message.Builder builder = ((Message) t).toBuilder();

            //传参查询key
            Object key = builder.getField(primaryDescriptor);
            String paramKey = queryKeysMap.get(key);
            if (paramKey == null) {
                key = RestUtils.docidDelTimeStamp(String.valueOf(key));
                paramKey = queryKeysMap.get(key);
            }
            return paramKey == null || key.equals(paramKey) ? builder.build()
                                                            : builder.setField(primaryDescriptor, paramKey).build();
        }).collect(Collectors.toMap(message -> message.getField(primaryDescriptor), Function.identity(),
                                    (v1, v2) -> v1));
    }

    /**
     * 销毁
     */
    @PreDestroy
    private void destory() {
        runableScheduled.set(false);
    }

    /**
     * 休眠方法
     *
     * @param time 休眠时间
     */
    private void sleep(long time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            log.error("sleep interruptedException", e);
        }
    }
}
