/*
 * Copyright © 2019 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2019湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.user.actinf;

import com.antfact.coordinator.swagger.EnableCoordinatorSwagger2;

import com.antdu.freon.user.actinf.consumer.ActinfUpdateConsumer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>spring boot启动入口 </p>
 * <p>
 * 创建日期： 2019/11/12
 *
 * <AUTHOR>
 */
@EnableCoordinatorSwagger2
@Slf4j
@SpringBootApplication
public class UserActinfApplication {

    /**
     * 数据初始化检查间隔时间
     */
    private static final int SLEEP_TIME = 5;

    /**
     * 入口方法
     *
     * @param args
     */
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(UserActinfApplication.class, args);
        ActinfUpdateConsumer updateConsumer = context.getBean(ActinfUpdateConsumer.class);
        HDFSDataReader hdfsDataReader = context.getBean(HDFSDataReader.class);
        boolean storageInited = context.getBean("storageInited", Boolean.class);
        //如果不需要读hdfs，则直接开始消费buka，否则，hdfs读取完成再消费
        if (storageInited) {
            updateConsumer.start();
            return;
        }
        while (!hdfsDataReader.isCompleted()) {
            try {
                TimeUnit.SECONDS.sleep(SLEEP_TIME);
            } catch (InterruptedException e) {
                log.error("sleep error", e);
            }
        }
        log.info("storage data init complete.");
        // 初始化完成后，启动消费者
        updateConsumer.start();
    }

}
