/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.cbase.driverclient.dto;

import com.datastax.oss.driver.api.core.cql.Row;

import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>widerow批量查询封装dto</p>
 * <p/>
 * 创建日期 2020/7/8
 *
 * @param <T> 实体类型
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WideRowBatchQueryParamDTO<T> {

    /**
     * 批量查询 keys
     */
    private Set<T> keys;

    /**
     * 查询结果集map集合
     */
    private Map<T, List<Row>> listRowMap;
}
