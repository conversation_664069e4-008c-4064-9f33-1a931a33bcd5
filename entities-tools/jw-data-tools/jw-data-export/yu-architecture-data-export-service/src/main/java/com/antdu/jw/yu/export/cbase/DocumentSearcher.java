/*
 * Copyright © 2018 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.jw.yu.export.cbase;

import com.antfact.commons.v2.model.StatusPack;

import com.antdu.freon.commons.utils.ProtobufMessageUtils;
import com.antdu.jw.yu.export.proto.FieldDescriptorHelper;
import com.google.protobuf.Descriptors;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;

/**
 * <p>网页查询器</p>
 * <p/>
 * 创建日期 2020/12/5
 *
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
@Component
public class DocumentSearcher extends SkinnyRowSearcher {

    /**
     * 博文实体字段映射
     */
    private Map<String, Descriptors.FieldDescriptor> descriptorMap;

    /**
     * protobuf 消息
     */
    private Message message;

    /**
     * 执行cql
     */
    private String executeCql;

    /**
     * 集群名
     */
    @Value("${export.cluster.document}")
    private String cluster;

    /**
     * 主键名
     */
    @Value("${export.className.document}")
    private String className;

    /**
     * 主键名
     */
    @Value("${export.primaryKeyName.document}")
    private String primaryKeyName;

    /**
     * 固定字段列表
     */
    @Value("${export.fixedField.document}")
    private String fixedField;

    /**
     * 命名空间
     */
    @Value("${export.keyspace.document}")
    private String keyspace;

    /**
     * 表名
     */
    @Value("${export.table.document}")
    private String table;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        descriptorMap = FieldDescriptorHelper.descriptorMap(className);
        message = ProtobufMessageUtils.getMessage(className);
        executeCql = FieldDescriptorHelper.skinnyQueryCql(
            primaryKeyName, keyspace, table, descriptorMap,
            FieldDescriptorHelper.fixedFieldSet(className, fixedField));
    }

    /**
     * 批量查询
     *
     * @param keys 查询key集合
     * @return 博文结果集
     */
    @SuppressWarnings("unchecked")
    public Map<String, StatusPack.Document> query(Set<String> keys) {
        return query(keys, cluster, s -> executeCql, (GeneratedMessageV3.Builder) message.newBuilderForType(),
                     descriptorMap);
    }

}
