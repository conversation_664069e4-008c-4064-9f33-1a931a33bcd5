/*
 * Copyright © 2021 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2021湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.cbase.driverclient.persist.skinny.special;

import com.antdu.freon.cbase.driverclient.persist.skinny.SkinnyRowStorage;
import com.google.protobuf.GeneratedMessageV3;

import org.springframework.stereotype.Component;

import java.util.Collection;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>持久时删读缓存持久器 </p>
 * <p>
 *
 * @param <T> 存储实体泛型
 * @param <B> 存储实体builder泛型
 * @param <R> 查询key泛型
 *            创建日期： 2021/4/8
 * <AUTHOR> tiantian(<EMAIL>)
 */
@Component
@Slf4j
public class CacheClearSkinnyRowStorage<T, B extends GeneratedMessageV3.Builder<B>, R>
    extends SkinnyRowStorage<T, B, R> {

    @Override
    protected void clearCache(Collection<T> collection) {
        collection.forEach(this::delReadCache);
    }
}
