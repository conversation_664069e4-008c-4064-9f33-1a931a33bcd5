/*
 * Copyright © 2022 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2022湖南蚁为软件有限公司。保留所有权利。
 */
package com.antdu.freon.entity.test;

import com.antfact.commons.v2.MessageSetUtils;
import com.antfact.commons.v2.model.StatusPack;

import org.apache.logging.log4j.util.Strings;
import org.junit.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>功能描述 </p>
 * <p>
 * 创建日期： 2022/8/30
 *
 * <AUTHOR> tiantian(<EMAIL>)
 */
public class QueryTest {

    @Test
    public void testDoc() throws IOException {
        String file = "/Users/<USER>/Desktop/docid";
        List<String> list = Files.readAllLines(new File(file).toPath());
        long begin = System.currentTimeMillis();
        int skip = 10000;
        int count = 0;
        int queryCount = 0;
        for (int i = skip; i < 5000 + skip; i = i + 100) {
            List<String> ids = list.subList(i, i + 100);
            String idStr = ids.stream().reduce((id1, id2) -> id1 + "," + id2).get();

            RestTemplate restTemplate = new RestTemplate();
            LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
            map.add("ids", idStr);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            byte[] bytes = restTemplate.postForObject("http://freon.dev-antducloud.com/document/batch/query",
                                                      new HttpEntity(map, headers), byte[].class);
            List<StatusPack.Document> documents = MessageSetUtils.fromByteArray(StatusPack.Document.class, bytes);
            byte[] bytes1 = restTemplate.postForObject("http://freon.dev-antducloud.com/mutaprop/batch/query",
                                                       new HttpEntity(map, headers), byte[].class);
            List<StatusPack.MutabilityProperties> mutaprop = MessageSetUtils.fromByteArray(StatusPack.MutabilityProperties.class, bytes1);

            List<String> originalIds = documents.stream().map(StatusPack.Document::getOriginalId).filter(Strings::isNotBlank).collect(Collectors.toList());

            if (originalIds.size() > 0) {
                LinkedMultiValueMap<String, Object> map1 = new LinkedMultiValueMap<>();
                String s = originalIds.stream().reduce((id1, id2) -> id1 + "," + id2).get();
                map1.add("ids", s);
                byte[] bytes2 = restTemplate.postForObject("http://freon.dev-antducloud.com/document/batch/query",
                                                           new HttpEntity(map1, headers), byte[].class);
                List<StatusPack.Document> documents2 = MessageSetUtils.fromByteArray(StatusPack.Document.class, bytes2);
            }

            count++;
            queryCount += (ids.size() * 2) + originalIds.size();

//            System.out.println("documents = " + documents.size() + " , originals = " + originalIds.size()
//                                   + " , query count = " + ((ids.size() * 2) + originalIds.size()) +
//                                   " , count = " + (i - skip + 100) + " ,id = " + documents.get(0).getDocId());
        }
        long end = System.currentTimeMillis();
        long time = end - begin;
        System.out.println("use time: " + time + " , avg time once: " + time / count + " , avg time: " + time / queryCount + " , query count: " + queryCount);
    }

    @Test
    public void testStatus() throws IOException {
        String file = "/Users/<USER>/Desktop/statusid";
        List<String> list = Files.readAllLines(new File(file).toPath());
        long begin = System.currentTimeMillis();
        int skip = 5000;
        int count = 0;
        int queryCount = 0;
        for (int i = skip; i < 5000 + skip; i = i + 100) {
            List<String> ids = list.subList(i, i + 100);
            String idStr = ids.stream().reduce((id1, id2) -> id1 + "," + id2).get();

            RestTemplate restTemplate = new RestTemplate();
            LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
            map.add("ids", idStr);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            byte[] bytes = restTemplate.postForObject("http://freon.dev-antducloud.com/status/batch/query",
                                                      new HttpEntity(map, headers), byte[].class);
            List<StatusPack.Status> statuses = MessageSetUtils.fromByteArray(StatusPack.Status.class, bytes);
            byte[] bytes1 = restTemplate.postForObject("http://freon.dev-antducloud.com/mutaprop/batch/query",
                                                       new HttpEntity(map, headers), byte[].class);
            List<StatusPack.MutabilityProperties> mutaprop = MessageSetUtils.fromByteArray(StatusPack.MutabilityProperties.class, bytes1);

            List<String> originalIds = statuses.stream().map(StatusPack.Status::getOriginalId).filter(Strings::isNotBlank).collect(Collectors.toList());

            if (originalIds.size() > 0) {
                LinkedMultiValueMap<String, Object> map1 = new LinkedMultiValueMap<>();
                String s = originalIds.stream().reduce((id1, id2) -> id1 + "," + id2).get();
                map1.add("ids", s);
                byte[] bytes2 = restTemplate.postForObject("http://freon.dev-antducloud.com/status/batch/query",
                                                           new HttpEntity(map1, headers), byte[].class);
                List<StatusPack.Status> statuses2 = MessageSetUtils.fromByteArray(StatusPack.Status.class, bytes2);
            }

            count++;
            queryCount += (ids.size() * 2) + originalIds.size();

//            System.out.println("documents = " + documents.size() + " , originals = " + originalIds.size()
//                                   + " , query count = " + ((ids.size() * 2) + originalIds.size()) +
//                                   " , count = " + (i - skip + 100) + " ,id = " + documents.get(0).getDocId());
        }
        long end = System.currentTimeMillis();
        long time = end - begin;
        System.out.println("use time: " + time + " , avg time once: " + time / count + " , avg time: " + time / queryCount + " , query count: " + queryCount);
    }
}
