/*
 * Copyright © 2019 Hunan Antdu Software Co., Ltd.. All rights reserved.
 * 版权所有©2018湖南蚁为软件有限公司。保留所有权利。
 */

package com.antdu.freon.circuitbreaker;

import com.antdu.freon.spi.CircuitBreaker;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.function.Supplier;

import io.github.resilience4j.timelimiter.TimeLimiter;
import io.vavr.control.Try;

/**
 * <p>熔断器实现</p>
 * <p/>
 * 创建日期 2019/4/23
 *
 * <AUTHOR> @ eefung.com)
 * <AUTHOR> pingzhu(<EMAIL>)
 * @since 1.0.0
 */
public class CircuitBreakerImpl implements CircuitBreaker {

    /**
     * 熔断器
     */
    private io.github.resilience4j.circuitbreaker.CircuitBreaker circuitBreaker;

    /**
     * 超时器
     */
    private TimeLimiter timeLimiter;

    /**
     * 线程池size
     */
    private static final Integer CORE_SIZE = 1;

    /**
     * 线程池
     */
    private ScheduledExecutorService scheduler =
        new ScheduledThreadPoolExecutor(
            CORE_SIZE, new ThreadFactoryBuilder().setDaemon(false).setNameFormat("circuitBreakerHelper-pool").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 构造函数
     *
     * @param circuitBreaker 熔断器
     * @param timeLimiter    超时器
     */
    public CircuitBreakerImpl(io.github.resilience4j.circuitbreaker.CircuitBreaker circuitBreaker,
                              TimeLimiter timeLimiter) {
        this.circuitBreaker = circuitBreaker;
        this.timeLimiter = timeLimiter;
    }

    @Override
    public <K, V> Function<K, V> of(Function<K, V> monitored, Function<K, V> compensation) {
        return param -> {

            //监控函数执行Supplier
            Supplier<CompletionStage<V>> supplier =
                () -> CompletableFuture.supplyAsync(() -> monitored.apply(param));

            //timelimiter 超时装饰
            Supplier<CompletionStage<V>> timelimiterstageSupplier =
                TimeLimiter.decorateCompletionStage(timeLimiter, scheduler, supplier);

            //circuitbreaker 熔断装饰
            CompletionStage<V> decorated =
                io.github.resilience4j.circuitbreaker.CircuitBreaker
                    .decorateCompletionStage(circuitBreaker, timelimiterstageSupplier)
                    .get();

            return Try.ofCallable(() -> decorated.toCompletableFuture().get())
                      .recover(ex -> compensation.apply(param))
                      .get();
        };
    }

    @Override
    public boolean isOpen() {
        return circuitBreaker.getState() == io.github.resilience4j.circuitbreaker.CircuitBreaker.State.OPEN;
    }


    @VisibleForTesting
    io.github.resilience4j.circuitbreaker.CircuitBreaker getCircuitBreaker() {
        return circuitBreaker;
    }
}
