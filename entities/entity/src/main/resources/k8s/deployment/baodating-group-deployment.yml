apiVersion: v1
kind: Service
metadata:
  name: freon-group-member-service
spec:
  selector:
    app: freon-baodating-group-member
  ports:
    - name: rest-port
      protocol: TCP
      port: 8994
      targetPort: 8994
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-baodating-group-member
  namespace:
  labels:
    app: freon-baodating-group-member
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-baodating-group-member
  template:
    metadata:
      labels:
        app: freon-baodating-group-member
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-baodating-group-member
          image: docker.antfact.com/platform/freon-entities-entity:1.0.0
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-group-member.yml,application-commons.yml"
            - "-Dapp_name=freon-group-member"
            - "-Denv=PRO"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          ports:
            - name: rest-port
              containerPort: 8994
          readinessProbe:
            httpGet:
              path: /group-member/1
              port: 8994
            initialDelaySeconds: 60
          resources:
            limits:
              cpu: "2"
              memory: 2Gi
          volumeMounts:
            - name: config
              mountPath: /home/<USER>
      volumes:
        - name: config
          configMap:
            name: freon-message-entities-cm
---
apiVersion: v1
kind: Service
metadata:
  name: freon-group-message-service
spec:
  selector:
    app: freon-baodating-group-message
  ports:
    - name: rest-port
      protocol: TCP
      port: 8568
      targetPort: 8568
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-baodating-group-message
  namespace:
  labels:
    app: freon-baodating-group-message
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-baodating-group-message
  template:
    metadata:
      labels:
        app: freon-baodating-group-message
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-baodating-group-message
          image: docker.antfact.com/platform/freon-entities-entity:1.0.0
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-group-message.yml,application-commons.yml"
            - "-Dapp_name=freon-group-message"
            - "-Denv=PRO"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          ports:
            - name: rest-port
              containerPort: 8568
          readinessProbe:
            httpGet:
              path: /group-message/1
              port: 8568
            initialDelaySeconds: 60
          resources:
            limits:
              cpu: "2"
              memory: 2Gi
          volumeMounts:
            - name: config
              mountPath: /home/<USER>
      volumes:
        - name: config
          configMap:
            name: freon-message-entities-cm
---
apiVersion: v1
kind: Service
metadata:
  name: freon-group-rooms-service
spec:
  selector:
    app: freon-baodating-group-rooms
  ports:
    - name: rest-port
      protocol: TCP
      port: 8995
      targetPort: 8995
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: freon-baodating-group-rooms
  namespace:
  labels:
    app: freon-baodating-group-rooms
spec:
  replicas: 1
  selector:
    matchLabels:
      app: freon-baodating-group-rooms
  template:
    metadata:
      labels:
        app: freon-baodating-group-rooms
    spec:
      imagePullSecrets:
        - name: antfact-secret
      containers:
        - name: freon-baodating-group-rooms
          image: docker.antfact.com/platform/freon-entities-entity:1.0.0
          imagePullPolicy: Always
          args:
            - "-XX:+UnlockExperimentalVMOptions"
            - "-XX:+UseCGroupMemoryLimitForHeap"
            - "-Ddapollo.bootstrap.namespaces=application-group-rooms.yml,application-commons.yml"
            - "-Dapp_name=freon-group-rooms"
            - "-Denv=PRO"
            - "-Dlog_home=./logs"
            - "-Dappend_ref=file"
            - "-Dlogging.config=config/log4j2.xml"
          ports:
            - name: rest-port
              containerPort: 8995
          readinessProbe:
            httpGet:
              path: /group-room/1
              port: 8995
            initialDelaySeconds: 60
          resources:
            limits:
              cpu: "2"
              memory: 2Gi
          volumeMounts:
            - name: config
              mountPath: /home/<USER>
      volumes:
        - name: config
          configMap:
            name: freon-message-entities-cm